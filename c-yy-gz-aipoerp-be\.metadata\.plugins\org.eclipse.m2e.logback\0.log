2025-06-16 08:59:00,854 [Worker-2: Loading available Gradle versions] INFO  o.e.b.c.i.u.g.PublishedGradleVersions - Gradle version information cache is not available. Remote download required.
2025-06-16 08:59:24,251 [Worker-22: Importing Maven projects] INFO  o.e.m.c.i.e.AbstractTransferListenerAdapter - Downloaded https://repo.maven.apache.org/maven2/com/yonyou/iuap/yonbip-2nd-parent/8.2.100-RELEASE/yonbip-2nd-parent-8.2.100-RELEASE.pom
2025-06-16 08:59:24,536 [Worker-22: Importing Maven projects] INFO  o.e.m.c.i.p.ProjectConfigurationManager - Imported and configured 5 project(s) in 1 sec
2025-06-16 09:01:08,353 [Worker-19: Updating Maven Project] INFO  o.e.m.c.i.p.ProjectConfigurationManager - Update started
2025-06-16 09:01:09,127 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:09,485 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:09,486 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:09,487 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:09,488 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:09,489 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:09,490 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:09,490 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:09,491 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:09,492 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:09,493 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:09,494 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:09,494 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:09,495 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:09,496 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:09,496 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:09,497 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:09,498 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:09,511 [Worker-19: Updating Maven Project] INFO  o.e.m.c.i.l.LifecycleMappingFactory - Using org.eclipse.m2e.jdt.JarLifecycleMapping lifecycle mapping for MavenProject: com.yonyou.ucf:c-yy-gz-aipoerp-api:ddm-3.0-RELEASE @ D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-api\pom.xml.
2025-06-16 09:01:10,852 [Worker-19: Updating Maven Project] INFO  o.e.m.c.i.p.WorkspaceClassifierResolverManager - Resolving P/dev-c-yy-gz-aipoerp-infrastructure: com.yonyou.ucf:c-yy-gz-aipoerp-infrastructure:ddm-3.0-RELEASE with classifier  to /dev-c-yy-gz-aipoerp-infrastructure/target/classes
2025-06-16 09:01:11,000 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:11,002 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:11,003 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:11,004 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:11,005 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:11,006 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:11,007 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:11,008 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:11,009 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:11,010 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:11,011 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:11,012 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:11,013 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:11,014 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:11,015 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:11,016 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:11,017 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:11,018 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:11,020 [Worker-19: Updating Maven Project] INFO  o.e.m.c.i.l.LifecycleMappingFactory - Using org.eclipse.m2e.jdt.JarLifecycleMapping lifecycle mapping for MavenProject: com.yonyou.ucf:c-yy-gz-aipoerp-app:ddm-3.0-RELEASE @ D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-app\pom.xml.
2025-06-16 09:01:11,386 [Worker-19: Updating Maven Project] INFO  o.e.m.c.i.p.WorkspaceClassifierResolverManager - Resolving P/dev-c-yy-gz-aipoerp-infrastructure: com.yonyou.ucf:c-yy-gz-aipoerp-infrastructure:ddm-3.0-RELEASE with classifier  to /dev-c-yy-gz-aipoerp-infrastructure/target/classes
2025-06-16 09:01:11,465 [Worker-19: Updating Maven Project] INFO  o.e.m.c.i.p.WorkspaceClassifierResolverManager - Resolving P/dev-c-yy-gz-aipoerp-service: com.yonyou.ucf:c-yy-gz-aipoerp-service:ddm-3.0-RELEASE with classifier  to /dev-c-yy-gz-aipoerp-service/target/classes
2025-06-16 09:01:11,510 [Worker-19: Updating Maven Project] INFO  o.e.m.c.i.p.WorkspaceClassifierResolverManager - Resolving P/dev-c-yy-gz-aipoerp-api: com.yonyou.ucf:c-yy-gz-aipoerp-api:ddm-3.0-RELEASE with classifier  to /dev-c-yy-gz-aipoerp-api/target/classes
2025-06-16 09:01:11,568 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:11,569 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:11,571 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:11,571 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:11,572 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:11,573 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:11,574 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:11,576 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:11,577 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:11,578 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:11,580 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:11,581 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:11,582 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:11,583 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:11,584 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:11,585 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:11,586 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:11,588 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:11,594 [Worker-19: Updating Maven Project] INFO  o.e.m.c.i.l.LifecycleMappingFactory - Using org.maven.ide.eclipse.wtp.WarLifecycleMapping lifecycle mapping for MavenProject: com.yonyou.ucf:c-yy-gz-aipoerp-bootstrap:ddm-3.0-RELEASE @ D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-bootstrap\pom.xml.
2025-06-16 09:01:11,680 [Worker-19: Updating Maven Project] INFO  o.e.m.c.i.p.WorkspaceClassifierResolverManager - Resolving P/dev-c-yy-gz-aipoerp-api: com.yonyou.ucf:c-yy-gz-aipoerp-api:ddm-3.0-RELEASE with classifier  to /dev-c-yy-gz-aipoerp-api/target/classes
2025-06-16 09:01:11,681 [Worker-19: Updating Maven Project] INFO  o.e.m.c.i.p.WorkspaceClassifierResolverManager - Resolving P/dev-c-yy-gz-aipoerp-app: com.yonyou.ucf:c-yy-gz-aipoerp-app:ddm-3.0-RELEASE with classifier  to /dev-c-yy-gz-aipoerp-app/target/classes
2025-06-16 09:01:11,682 [Worker-19: Updating Maven Project] INFO  o.e.m.c.i.p.WorkspaceClassifierResolverManager - Resolving P/dev-c-yy-gz-aipoerp-infrastructure: com.yonyou.ucf:c-yy-gz-aipoerp-infrastructure:ddm-3.0-RELEASE with classifier  to /dev-c-yy-gz-aipoerp-infrastructure/target/classes
2025-06-16 09:01:11,755 [Worker-19: Updating Maven Project] INFO  o.e.m.c.i.p.WorkspaceClassifierResolverManager - Resolving P/dev-c-yy-gz-aipoerp-service: com.yonyou.ucf:c-yy-gz-aipoerp-service:ddm-3.0-RELEASE with classifier  to /dev-c-yy-gz-aipoerp-service/target/classes
2025-06-16 09:01:11,841 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:11,842 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:11,842 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:11,843 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:11,843 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:11,844 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:11,844 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:11,845 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:11,846 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:11,847 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:11,847 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:11,848 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:11,849 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:11,849 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:11,850 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:11,850 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:11,851 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:11,852 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:11,852 [Worker-19: Updating Maven Project] INFO  o.e.m.c.i.l.LifecycleMappingFactory - Using org.eclipse.m2e.jdt.JarLifecycleMapping lifecycle mapping for MavenProject: com.yonyou.ucf:c-yy-gz-aipoerp-infrastructure:ddm-3.0-RELEASE @ D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-infrastructure\pom.xml.
2025-06-16 09:01:12,020 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:12,022 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:12,023 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:12,023 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:12,024 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:12,024 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:12,025 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:12,025 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:12,026 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:12,027 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:12,027 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:12,028 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:12,029 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:12,029 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:12,030 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:12,031 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:12,032 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:12,033 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:12,034 [Worker-19: Updating Maven Project] INFO  o.e.m.c.i.l.LifecycleMappingFactory - Using org.eclipse.m2e.jdt.JarLifecycleMapping lifecycle mapping for MavenProject: com.yonyou.ucf:c-yy-gz-aipoerp-service:ddm-3.0-RELEASE @ D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-service\pom.xml.
2025-06-16 09:01:12,219 [Worker-19: Updating Maven Project] INFO  o.e.m.c.i.p.WorkspaceClassifierResolverManager - Resolving P/dev-c-yy-gz-aipoerp-api: com.yonyou.ucf:c-yy-gz-aipoerp-api:ddm-3.0-RELEASE with classifier  to /dev-c-yy-gz-aipoerp-api/target/classes
2025-06-16 09:01:12,220 [Worker-19: Updating Maven Project] INFO  o.e.m.c.i.p.WorkspaceClassifierResolverManager - Resolving P/dev-c-yy-gz-aipoerp-infrastructure: com.yonyou.ucf:c-yy-gz-aipoerp-infrastructure:ddm-3.0-RELEASE with classifier  to /dev-c-yy-gz-aipoerp-infrastructure/target/classes
2025-06-16 09:01:12,261 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:12,262 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:12,263 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:12,263 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:12,264 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:12,264 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:12,265 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:12,265 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:12,266 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:12,266 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:12,267 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:12,267 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:12,268 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:12,268 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:12,269 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:12,269 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:12,270 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:12,271 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:12,272 [Worker-19: Updating Maven Project] INFO  o.e.m.c.i.l.LifecycleMappingFactory - Using org.eclipse.m2e.jdt.JarLifecycleMapping lifecycle mapping for MavenProject: com.yonyou.ucf:c-yy-gz-aipoerp-api:ddm-3.0-RELEASE @ D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-api\pom.xml.
2025-06-16 09:01:12,304 [Worker-19: Updating Maven Project] INFO  o.e.m.c.i.p.WorkspaceClassifierResolverManager - Resolving P/dev-c-yy-gz-aipoerp-infrastructure: com.yonyou.ucf:c-yy-gz-aipoerp-infrastructure:ddm-3.0-RELEASE with classifier  to /dev-c-yy-gz-aipoerp-infrastructure/target/classes
2025-06-16 09:01:12,447 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:12,448 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:12,448 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:12,449 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:12,450 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:12,450 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:12,451 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:12,452 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:12,452 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:12,453 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:12,454 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:12,454 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:12,456 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:12,456 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:12,457 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:12,457 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:12,458 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:12,459 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:12,460 [Worker-19: Updating Maven Project] INFO  o.e.m.c.i.l.LifecycleMappingFactory - Using org.eclipse.m2e.jdt.JarLifecycleMapping lifecycle mapping for MavenProject: com.yonyou.ucf:c-yy-gz-aipoerp-app:ddm-3.0-RELEASE @ D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-app\pom.xml.
2025-06-16 09:01:12,524 [Worker-19: Updating Maven Project] INFO  o.e.m.c.i.p.WorkspaceClassifierResolverManager - Resolving P/dev-c-yy-gz-aipoerp-infrastructure: com.yonyou.ucf:c-yy-gz-aipoerp-infrastructure:ddm-3.0-RELEASE with classifier  to /dev-c-yy-gz-aipoerp-infrastructure/target/classes
2025-06-16 09:01:12,597 [Worker-19: Updating Maven Project] INFO  o.e.m.c.i.p.WorkspaceClassifierResolverManager - Resolving P/dev-c-yy-gz-aipoerp-service: com.yonyou.ucf:c-yy-gz-aipoerp-service:ddm-3.0-RELEASE with classifier  to /dev-c-yy-gz-aipoerp-service/target/classes
2025-06-16 09:01:12,641 [Worker-19: Updating Maven Project] INFO  o.e.m.c.i.p.WorkspaceClassifierResolverManager - Resolving P/dev-c-yy-gz-aipoerp-api: com.yonyou.ucf:c-yy-gz-aipoerp-api:ddm-3.0-RELEASE with classifier  to /dev-c-yy-gz-aipoerp-api/target/classes
2025-06-16 09:01:12,684 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:12,685 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:12,686 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:12,687 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:12,688 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:12,689 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:12,689 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:12,690 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:12,691 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:12,692 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:12,692 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:12,693 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:12,694 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:12,694 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:12,695 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:12,695 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:12,696 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:12,696 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:12,697 [Worker-19: Updating Maven Project] INFO  o.e.m.c.i.l.LifecycleMappingFactory - Using org.maven.ide.eclipse.wtp.WarLifecycleMapping lifecycle mapping for MavenProject: com.yonyou.ucf:c-yy-gz-aipoerp-bootstrap:ddm-3.0-RELEASE @ D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-bootstrap\pom.xml.
2025-06-16 09:01:12,763 [Worker-19: Updating Maven Project] INFO  o.e.m.c.i.p.WorkspaceClassifierResolverManager - Resolving P/dev-c-yy-gz-aipoerp-api: com.yonyou.ucf:c-yy-gz-aipoerp-api:ddm-3.0-RELEASE with classifier  to /dev-c-yy-gz-aipoerp-api/target/classes
2025-06-16 09:01:12,764 [Worker-19: Updating Maven Project] INFO  o.e.m.c.i.p.WorkspaceClassifierResolverManager - Resolving P/dev-c-yy-gz-aipoerp-app: com.yonyou.ucf:c-yy-gz-aipoerp-app:ddm-3.0-RELEASE with classifier  to /dev-c-yy-gz-aipoerp-app/target/classes
2025-06-16 09:01:12,764 [Worker-19: Updating Maven Project] INFO  o.e.m.c.i.p.WorkspaceClassifierResolverManager - Resolving P/dev-c-yy-gz-aipoerp-infrastructure: com.yonyou.ucf:c-yy-gz-aipoerp-infrastructure:ddm-3.0-RELEASE with classifier  to /dev-c-yy-gz-aipoerp-infrastructure/target/classes
2025-06-16 09:01:12,836 [Worker-19: Updating Maven Project] INFO  o.e.m.c.i.p.WorkspaceClassifierResolverManager - Resolving P/dev-c-yy-gz-aipoerp-service: com.yonyou.ucf:c-yy-gz-aipoerp-service:ddm-3.0-RELEASE with classifier  to /dev-c-yy-gz-aipoerp-service/target/classes
2025-06-16 09:01:12,918 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:12,919 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:12,920 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:12,920 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:12,921 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:12,921 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:12,922 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:12,923 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:12,924 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:12,925 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:12,925 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:12,926 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:12,926 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:12,927 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:12,927 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:12,928 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:12,928 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:12,929 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:12,929 [Worker-19: Updating Maven Project] INFO  o.e.m.c.i.l.LifecycleMappingFactory - Using org.eclipse.m2e.jdt.JarLifecycleMapping lifecycle mapping for MavenProject: com.yonyou.ucf:c-yy-gz-aipoerp-service:ddm-3.0-RELEASE @ D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-service\pom.xml.
2025-06-16 09:01:13,104 [Worker-19: Updating Maven Project] INFO  o.e.m.c.i.p.WorkspaceClassifierResolverManager - Resolving P/dev-c-yy-gz-aipoerp-api: com.yonyou.ucf:c-yy-gz-aipoerp-api:ddm-3.0-RELEASE with classifier  to /dev-c-yy-gz-aipoerp-api/target/classes
2025-06-16 09:01:13,105 [Worker-19: Updating Maven Project] INFO  o.e.m.c.i.p.WorkspaceClassifierResolverManager - Resolving P/dev-c-yy-gz-aipoerp-infrastructure: com.yonyou.ucf:c-yy-gz-aipoerp-infrastructure:ddm-3.0-RELEASE with classifier  to /dev-c-yy-gz-aipoerp-infrastructure/target/classes
2025-06-16 09:01:13,142 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:13,143 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:13,143 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:13,144 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:13,145 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:13,146 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:13,146 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:13,147 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:13,148 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:13,148 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:13,149 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:13,149 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:13,150 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:13,151 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:13,151 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:13,151 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:13,152 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:13,152 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:13,153 [Worker-19: Updating Maven Project] INFO  o.e.m.c.i.l.LifecycleMappingFactory - Using org.eclipse.m2e.jdt.JarLifecycleMapping lifecycle mapping for MavenProject: com.yonyou.ucf:c-yy-gz-aipoerp-app:ddm-3.0-RELEASE @ D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-app\pom.xml.
2025-06-16 09:01:13,215 [Worker-19: Updating Maven Project] INFO  o.e.m.c.i.p.WorkspaceClassifierResolverManager - Resolving P/dev-c-yy-gz-aipoerp-infrastructure: com.yonyou.ucf:c-yy-gz-aipoerp-infrastructure:ddm-3.0-RELEASE with classifier  to /dev-c-yy-gz-aipoerp-infrastructure/target/classes
2025-06-16 09:01:13,287 [Worker-19: Updating Maven Project] INFO  o.e.m.c.i.p.WorkspaceClassifierResolverManager - Resolving P/dev-c-yy-gz-aipoerp-service: com.yonyou.ucf:c-yy-gz-aipoerp-service:ddm-3.0-RELEASE with classifier  to /dev-c-yy-gz-aipoerp-service/target/classes
2025-06-16 09:01:13,332 [Worker-19: Updating Maven Project] INFO  o.e.m.c.i.p.WorkspaceClassifierResolverManager - Resolving P/dev-c-yy-gz-aipoerp-api: com.yonyou.ucf:c-yy-gz-aipoerp-api:ddm-3.0-RELEASE with classifier  to /dev-c-yy-gz-aipoerp-api/target/classes
2025-06-16 09:01:13,370 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:13,371 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:13,371 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:13,372 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:13,372 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:13,372 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:13,373 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:13,373 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:13,374 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:13,374 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:13,375 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:13,375 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:13,376 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:13,376 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:13,376 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:13,377 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:13,377 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:13,378 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:13,378 [Worker-19: Updating Maven Project] INFO  o.e.m.c.i.l.LifecycleMappingFactory - Using org.maven.ide.eclipse.wtp.WarLifecycleMapping lifecycle mapping for MavenProject: com.yonyou.ucf:c-yy-gz-aipoerp-bootstrap:ddm-3.0-RELEASE @ D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-bootstrap\pom.xml.
2025-06-16 09:01:13,442 [Worker-19: Updating Maven Project] INFO  o.e.m.c.i.p.WorkspaceClassifierResolverManager - Resolving P/dev-c-yy-gz-aipoerp-api: com.yonyou.ucf:c-yy-gz-aipoerp-api:ddm-3.0-RELEASE with classifier  to /dev-c-yy-gz-aipoerp-api/target/classes
2025-06-16 09:01:13,443 [Worker-19: Updating Maven Project] INFO  o.e.m.c.i.p.WorkspaceClassifierResolverManager - Resolving P/dev-c-yy-gz-aipoerp-app: com.yonyou.ucf:c-yy-gz-aipoerp-app:ddm-3.0-RELEASE with classifier  to /dev-c-yy-gz-aipoerp-app/target/classes
2025-06-16 09:01:13,443 [Worker-19: Updating Maven Project] INFO  o.e.m.c.i.p.WorkspaceClassifierResolverManager - Resolving P/dev-c-yy-gz-aipoerp-infrastructure: com.yonyou.ucf:c-yy-gz-aipoerp-infrastructure:ddm-3.0-RELEASE with classifier  to /dev-c-yy-gz-aipoerp-infrastructure/target/classes
2025-06-16 09:01:13,513 [Worker-19: Updating Maven Project] INFO  o.e.m.c.i.p.WorkspaceClassifierResolverManager - Resolving P/dev-c-yy-gz-aipoerp-service: com.yonyou.ucf:c-yy-gz-aipoerp-service:ddm-3.0-RELEASE with classifier  to /dev-c-yy-gz-aipoerp-service/target/classes
2025-06-16 09:01:13,596 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:13,596 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:13,976 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:13,977 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:13,988 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:13,989 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:14,338 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:14,339 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:14,751 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:14,752 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:16,376 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:16,380 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:16,453 [Worker-19: Updating Maven Project] INFO  o.e.m.j.i.AbstractJavaProjectConfigurator - Adding source folder /dev-c-yy-gz-aipoerp-api/src/main/java
2025-06-16 09:01:16,455 [Worker-19: Updating Maven Project] INFO  o.e.m.j.i.AbstractJavaProjectConfigurator - Adding resource folder /dev-c-yy-gz-aipoerp-api/src/main/resources
2025-06-16 09:01:16,456 [Worker-19: Updating Maven Project] INFO  o.e.m.j.i.AbstractJavaProjectConfigurator - Adding resource folder /dev-c-yy-gz-aipoerp-api/src/main/resources
2025-06-16 09:01:16,456 [Worker-19: Updating Maven Project] INFO  o.e.m.j.i.AbstractJavaProjectConfigurator - Adding source folder /dev-c-yy-gz-aipoerp-api/src/test/java
2025-06-16 09:01:16,457 [Worker-19: Updating Maven Project] INFO  o.e.m.j.i.AbstractJavaProjectConfigurator - Adding resource folder /dev-c-yy-gz-aipoerp-api/src/test/resources
2025-06-16 09:01:16,729 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:16,730 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:16,757 [Worker-19: Updating Maven Project] INFO  o.e.m.j.i.AbstractJavaProjectConfigurator - Adding source folder /dev-c-yy-gz-aipoerp-app/src/main/java
2025-06-16 09:01:16,758 [Worker-19: Updating Maven Project] INFO  o.e.m.j.i.AbstractJavaProjectConfigurator - Adding resource folder /dev-c-yy-gz-aipoerp-app/src/main/resources
2025-06-16 09:01:16,758 [Worker-19: Updating Maven Project] INFO  o.e.m.j.i.AbstractJavaProjectConfigurator - Adding resource folder /dev-c-yy-gz-aipoerp-app/src/main/resources
2025-06-16 09:01:16,761 [Worker-19: Updating Maven Project] INFO  o.e.m.j.i.AbstractJavaProjectConfigurator - Adding source folder /dev-c-yy-gz-aipoerp-app/src/test/java
2025-06-16 09:01:16,762 [Worker-19: Updating Maven Project] INFO  o.e.m.j.i.AbstractJavaProjectConfigurator - Adding resource folder /dev-c-yy-gz-aipoerp-app/src/test/resources
2025-06-16 09:01:17,080 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:17,081 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:17,176 [Worker-19: Updating Maven Project] INFO  o.e.m.j.i.AbstractJavaProjectConfigurator - Adding source folder /dev-c-yy-gz-aipoerp-bootstrap/src/main/java
2025-06-16 09:01:17,177 [Worker-19: Updating Maven Project] INFO  o.e.m.j.i.AbstractJavaProjectConfigurator - Adding resource folder /dev-c-yy-gz-aipoerp-bootstrap/src/main/resources
2025-06-16 09:01:17,177 [Worker-19: Updating Maven Project] INFO  o.e.m.j.i.AbstractJavaProjectConfigurator - Adding resource folder /dev-c-yy-gz-aipoerp-bootstrap/src/main/resources
2025-06-16 09:01:17,178 [Worker-19: Updating Maven Project] INFO  o.e.m.j.i.AbstractJavaProjectConfigurator - Adding source folder /dev-c-yy-gz-aipoerp-bootstrap/src/test/java
2025-06-16 09:01:17,178 [Worker-19: Updating Maven Project] INFO  o.e.m.j.i.AbstractJavaProjectConfigurator - Adding resource folder /dev-c-yy-gz-aipoerp-bootstrap/src/test/resources
2025-06-16 09:01:23,564 [Worker-19: Updating Maven Project] INFO  o.e.m.c.i.p.WorkspaceClassifierResolverManager - Resolving P/dev-c-yy-gz-aipoerp-api: com.yonyou.ucf:c-yy-gz-aipoerp-api:ddm-3.0-RELEASE with classifier  to /dev-c-yy-gz-aipoerp-api/target/classes
2025-06-16 09:01:23,566 [Worker-19: Updating Maven Project] INFO  o.e.m.c.i.p.WorkspaceClassifierResolverManager - Resolving P/dev-c-yy-gz-aipoerp-app: com.yonyou.ucf:c-yy-gz-aipoerp-app:ddm-3.0-RELEASE with classifier  to /dev-c-yy-gz-aipoerp-app/target/classes
2025-06-16 09:01:23,566 [Worker-19: Updating Maven Project] INFO  o.e.m.c.i.p.WorkspaceClassifierResolverManager - Resolving P/dev-c-yy-gz-aipoerp-infrastructure: com.yonyou.ucf:c-yy-gz-aipoerp-infrastructure:ddm-3.0-RELEASE with classifier  to /dev-c-yy-gz-aipoerp-infrastructure/target/classes
2025-06-16 09:01:23,643 [Worker-19: Updating Maven Project] INFO  o.e.m.c.i.p.WorkspaceClassifierResolverManager - Resolving P/dev-c-yy-gz-aipoerp-service: com.yonyou.ucf:c-yy-gz-aipoerp-service:ddm-3.0-RELEASE with classifier  to /dev-c-yy-gz-aipoerp-service/target/classes
2025-06-16 09:01:24,345 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:24,347 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:24,379 [Worker-19: Updating Maven Project] INFO  o.e.m.j.i.AbstractJavaProjectConfigurator - Adding source folder /dev-c-yy-gz-aipoerp-infrastructure/src/main/java
2025-06-16 09:01:24,380 [Worker-19: Updating Maven Project] INFO  o.e.m.j.i.AbstractJavaProjectConfigurator - Adding resource folder /dev-c-yy-gz-aipoerp-infrastructure/src/main/resources
2025-06-16 09:01:24,380 [Worker-19: Updating Maven Project] INFO  o.e.m.j.i.AbstractJavaProjectConfigurator - Adding resource folder /dev-c-yy-gz-aipoerp-infrastructure/src/main/resources
2025-06-16 09:01:24,381 [Worker-19: Updating Maven Project] INFO  o.e.m.j.i.AbstractJavaProjectConfigurator - Adding source folder /dev-c-yy-gz-aipoerp-infrastructure/src/test/java
2025-06-16 09:01:24,381 [Worker-19: Updating Maven Project] INFO  o.e.m.j.i.AbstractJavaProjectConfigurator - Adding resource folder /dev-c-yy-gz-aipoerp-infrastructure/src/test/resources
2025-06-16 09:01:24,659 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:24,660 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:24,733 [Worker-19: Updating Maven Project] INFO  o.e.m.j.i.AbstractJavaProjectConfigurator - Adding source folder /dev-c-yy-gz-aipoerp-service/src/main/java
2025-06-16 09:01:24,734 [Worker-19: Updating Maven Project] INFO  o.e.m.j.i.AbstractJavaProjectConfigurator - Adding resource folder /dev-c-yy-gz-aipoerp-service/src/main/resources
2025-06-16 09:01:24,738 [Worker-19: Updating Maven Project] INFO  o.e.m.j.i.AbstractJavaProjectConfigurator - Adding resource folder /dev-c-yy-gz-aipoerp-service/src/main/resources
2025-06-16 09:01:24,740 [Worker-19: Updating Maven Project] INFO  o.e.m.j.i.AbstractJavaProjectConfigurator - Adding source folder /dev-c-yy-gz-aipoerp-service/src/test/java
2025-06-16 09:01:24,741 [Worker-19: Updating Maven Project] INFO  o.e.m.j.i.AbstractJavaProjectConfigurator - Adding resource folder /dev-c-yy-gz-aipoerp-service/src/test/resources
2025-06-16 09:01:25,469 [Worker-19: Updating Maven Project] INFO  o.e.m.w.i.f.ResourceFilteringBuildParticipant - Cleaning filtered folder for dev-c-yy-gz-aipoerp-bootstrap
2025-06-16 09:01:25,689 [Worker-19: Updating Maven Project] INFO  o.e.m.c.i.p.ProjectConfigurationManager - Update completed for 5 poms: local refresh takes 0.00 sec, refresh facades takes 7.00 sec, update config takes 8.00 sec 
2025-06-16 09:01:26,231 [Worker-19: Updating Maven Project] INFO  o.e.m.c.i.p.ProjectConfigurationManager - Update started
2025-06-16 09:01:26,545 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:26,547 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:26,548 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:26,549 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:26,550 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:26,551 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:26,552 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:26,553 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:26,554 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:26,555 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:26,556 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:26,558 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:26,558 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:26,559 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:26,560 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:26,561 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:26,562 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:26,563 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:26,564 [Worker-19: Updating Maven Project] INFO  o.e.m.c.i.l.LifecycleMappingFactory - Using org.eclipse.m2e.jdt.JarLifecycleMapping lifecycle mapping for MavenProject: com.yonyou.ucf:c-yy-gz-aipoerp-api:ddm-3.0-RELEASE @ D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-api\pom.xml.
2025-06-16 09:01:27,636 [Worker-19: Updating Maven Project] INFO  o.e.m.c.i.p.WorkspaceClassifierResolverManager - Resolving P/dev-c-yy-gz-aipoerp-infrastructure: com.yonyou.ucf:c-yy-gz-aipoerp-infrastructure:ddm-3.0-RELEASE with classifier  to /dev-c-yy-gz-aipoerp-infrastructure/target/classes
2025-06-16 09:01:27,788 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:27,790 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:27,791 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:27,792 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:27,792 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:27,793 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:27,794 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:27,794 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:27,796 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:27,797 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:27,798 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:27,799 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:27,800 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:27,800 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:27,801 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:27,802 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:27,803 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:27,803 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:27,805 [Worker-19: Updating Maven Project] INFO  o.e.m.c.i.l.LifecycleMappingFactory - Using org.eclipse.m2e.jdt.JarLifecycleMapping lifecycle mapping for MavenProject: com.yonyou.ucf:c-yy-gz-aipoerp-app:ddm-3.0-RELEASE @ D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-app\pom.xml.
2025-06-16 09:01:28,056 [Worker-19: Updating Maven Project] INFO  o.e.m.c.i.p.WorkspaceClassifierResolverManager - Resolving P/dev-c-yy-gz-aipoerp-infrastructure: com.yonyou.ucf:c-yy-gz-aipoerp-infrastructure:ddm-3.0-RELEASE with classifier  to /dev-c-yy-gz-aipoerp-infrastructure/target/classes
2025-06-16 09:01:28,128 [Worker-19: Updating Maven Project] INFO  o.e.m.c.i.p.WorkspaceClassifierResolverManager - Resolving P/dev-c-yy-gz-aipoerp-service: com.yonyou.ucf:c-yy-gz-aipoerp-service:ddm-3.0-RELEASE with classifier  to /dev-c-yy-gz-aipoerp-service/target/classes
2025-06-16 09:01:28,170 [Worker-19: Updating Maven Project] INFO  o.e.m.c.i.p.WorkspaceClassifierResolverManager - Resolving P/dev-c-yy-gz-aipoerp-api: com.yonyou.ucf:c-yy-gz-aipoerp-api:ddm-3.0-RELEASE with classifier  to /dev-c-yy-gz-aipoerp-api/target/classes
2025-06-16 09:01:28,210 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:28,211 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:28,213 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:28,213 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:28,214 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:28,215 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:28,215 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:28,216 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:28,216 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:28,217 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:28,218 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:28,218 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:28,219 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:28,219 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:28,220 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:28,221 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:28,221 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:28,222 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:28,224 [Worker-19: Updating Maven Project] INFO  o.e.m.c.i.l.LifecycleMappingFactory - Using org.eclipse.m2e.jdt.JarLifecycleMapping lifecycle mapping for MavenProject: com.yonyou.ucf:c-yy-gz-aipoerp-infrastructure:ddm-3.0-RELEASE @ D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-infrastructure\pom.xml.
2025-06-16 09:01:28,379 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:28,380 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:28,419 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:28,421 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:28,423 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:28,423 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:28,441 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:28,442 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:28,445 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:28,446 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:28,461 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:28,462 [Worker-19: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:29,122 [Worker-19: Updating Maven Project] INFO  o.e.m.j.i.AbstractJavaProjectConfigurator - Adding source folder /dev-c-yy-gz-aipoerp-api/src/main/java
2025-06-16 09:01:29,123 [Worker-19: Updating Maven Project] INFO  o.e.m.j.i.AbstractJavaProjectConfigurator - Adding resource folder /dev-c-yy-gz-aipoerp-api/src/main/resources
2025-06-16 09:01:29,123 [Worker-19: Updating Maven Project] INFO  o.e.m.j.i.AbstractJavaProjectConfigurator - Adding resource folder /dev-c-yy-gz-aipoerp-api/src/main/resources
2025-06-16 09:01:29,124 [Worker-19: Updating Maven Project] INFO  o.e.m.j.i.AbstractJavaProjectConfigurator - Adding source folder /dev-c-yy-gz-aipoerp-api/src/test/java
2025-06-16 09:01:29,124 [Worker-19: Updating Maven Project] INFO  o.e.m.j.i.AbstractJavaProjectConfigurator - Adding resource folder /dev-c-yy-gz-aipoerp-api/src/test/resources
2025-06-16 09:01:29,418 [Worker-19: Updating Maven Project] INFO  o.e.m.j.i.AbstractJavaProjectConfigurator - Adding source folder /dev-c-yy-gz-aipoerp-app/src/main/java
2025-06-16 09:01:29,418 [Worker-19: Updating Maven Project] INFO  o.e.m.j.i.AbstractJavaProjectConfigurator - Adding resource folder /dev-c-yy-gz-aipoerp-app/src/main/resources
2025-06-16 09:01:29,419 [Worker-19: Updating Maven Project] INFO  o.e.m.j.i.AbstractJavaProjectConfigurator - Adding resource folder /dev-c-yy-gz-aipoerp-app/src/main/resources
2025-06-16 09:01:29,419 [Worker-19: Updating Maven Project] INFO  o.e.m.j.i.AbstractJavaProjectConfigurator - Adding source folder /dev-c-yy-gz-aipoerp-app/src/test/java
2025-06-16 09:01:29,420 [Worker-19: Updating Maven Project] INFO  o.e.m.j.i.AbstractJavaProjectConfigurator - Adding resource folder /dev-c-yy-gz-aipoerp-app/src/test/resources
2025-06-16 09:01:29,719 [Worker-19: Updating Maven Project] INFO  o.e.m.j.i.AbstractJavaProjectConfigurator - Adding source folder /dev-c-yy-gz-aipoerp-infrastructure/src/main/java
2025-06-16 09:01:29,720 [Worker-19: Updating Maven Project] INFO  o.e.m.j.i.AbstractJavaProjectConfigurator - Adding resource folder /dev-c-yy-gz-aipoerp-infrastructure/src/main/resources
2025-06-16 09:01:29,720 [Worker-19: Updating Maven Project] INFO  o.e.m.j.i.AbstractJavaProjectConfigurator - Adding resource folder /dev-c-yy-gz-aipoerp-infrastructure/src/main/resources
2025-06-16 09:01:29,721 [Worker-19: Updating Maven Project] INFO  o.e.m.j.i.AbstractJavaProjectConfigurator - Adding source folder /dev-c-yy-gz-aipoerp-infrastructure/src/test/java
2025-06-16 09:01:29,721 [Worker-19: Updating Maven Project] INFO  o.e.m.j.i.AbstractJavaProjectConfigurator - Adding resource folder /dev-c-yy-gz-aipoerp-infrastructure/src/test/resources
2025-06-16 09:01:30,100 [Worker-19: Updating Maven Project] INFO  o.e.m.c.i.p.ProjectConfigurationManager - Update completed for 3 poms: local refresh takes 0.00 sec, refresh facades takes 2.00 sec, update config takes 0.00 sec 
2025-06-16 09:01:30,114 [Worker-1: Updating Maven Project] INFO  o.e.m.c.i.p.ProjectConfigurationManager - Update started
2025-06-16 09:01:30,358 [Worker-1: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:30,359 [Worker-1: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:30,360 [Worker-1: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:30,361 [Worker-1: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:30,362 [Worker-1: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:30,363 [Worker-1: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:30,364 [Worker-1: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:30,364 [Worker-1: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:30,365 [Worker-1: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:30,366 [Worker-1: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:30,366 [Worker-1: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:30,367 [Worker-1: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:30,367 [Worker-1: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:30,368 [Worker-1: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:30,368 [Worker-1: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:30,369 [Worker-1: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:30,370 [Worker-1: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:30,370 [Worker-1: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:30,372 [Worker-1: Updating Maven Project] INFO  o.e.m.c.i.l.LifecycleMappingFactory - Using org.maven.ide.eclipse.wtp.WarLifecycleMapping lifecycle mapping for MavenProject: com.yonyou.ucf:c-yy-gz-aipoerp-bootstrap:ddm-3.0-RELEASE @ D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-bootstrap\pom.xml.
2025-06-16 09:01:31,451 [Worker-1: Updating Maven Project] INFO  o.e.m.c.i.p.WorkspaceClassifierResolverManager - Resolving P/dev-c-yy-gz-aipoerp-api: com.yonyou.ucf:c-yy-gz-aipoerp-api:ddm-3.0-RELEASE with classifier  to /dev-c-yy-gz-aipoerp-api/target/classes
2025-06-16 09:01:31,452 [Worker-1: Updating Maven Project] INFO  o.e.m.c.i.p.WorkspaceClassifierResolverManager - Resolving P/dev-c-yy-gz-aipoerp-app: com.yonyou.ucf:c-yy-gz-aipoerp-app:ddm-3.0-RELEASE with classifier  to /dev-c-yy-gz-aipoerp-app/target/classes
2025-06-16 09:01:31,452 [Worker-1: Updating Maven Project] INFO  o.e.m.c.i.p.WorkspaceClassifierResolverManager - Resolving P/dev-c-yy-gz-aipoerp-infrastructure: com.yonyou.ucf:c-yy-gz-aipoerp-infrastructure:ddm-3.0-RELEASE with classifier  to /dev-c-yy-gz-aipoerp-infrastructure/target/classes
2025-06-16 09:01:31,522 [Worker-1: Updating Maven Project] INFO  o.e.m.c.i.p.WorkspaceClassifierResolverManager - Resolving P/dev-c-yy-gz-aipoerp-service: com.yonyou.ucf:c-yy-gz-aipoerp-service:ddm-3.0-RELEASE with classifier  to /dev-c-yy-gz-aipoerp-service/target/classes
2025-06-16 09:01:31,605 [Worker-1: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:31,606 [Worker-1: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:31,607 [Worker-1: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:31,607 [Worker-1: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:31,608 [Worker-1: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:31,608 [Worker-1: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:31,609 [Worker-1: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:31,609 [Worker-1: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:31,610 [Worker-1: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:31,611 [Worker-1: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:31,611 [Worker-1: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:31,612 [Worker-1: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:31,612 [Worker-1: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:31,613 [Worker-1: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:31,613 [Worker-1: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:31,614 [Worker-1: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:31,615 [Worker-1: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:31,616 [Worker-1: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:31,617 [Worker-1: Updating Maven Project] INFO  o.e.m.c.i.l.LifecycleMappingFactory - Using org.eclipse.m2e.jdt.JarLifecycleMapping lifecycle mapping for MavenProject: com.yonyou.ucf:c-yy-gz-aipoerp-service:ddm-3.0-RELEASE @ D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-service\pom.xml.
2025-06-16 09:01:31,807 [Worker-1: Updating Maven Project] INFO  o.e.m.c.i.p.WorkspaceClassifierResolverManager - Resolving P/dev-c-yy-gz-aipoerp-api: com.yonyou.ucf:c-yy-gz-aipoerp-api:ddm-3.0-RELEASE with classifier  to /dev-c-yy-gz-aipoerp-api/target/classes
2025-06-16 09:01:31,808 [Worker-1: Updating Maven Project] INFO  o.e.m.c.i.p.WorkspaceClassifierResolverManager - Resolving P/dev-c-yy-gz-aipoerp-infrastructure: com.yonyou.ucf:c-yy-gz-aipoerp-infrastructure:ddm-3.0-RELEASE with classifier  to /dev-c-yy-gz-aipoerp-infrastructure/target/classes
2025-06-16 09:01:31,915 [Worker-1: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:31,916 [Worker-1: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:32,326 [Worker-1: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:32,328 [Worker-1: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:32,330 [Worker-1: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:32,331 [Worker-1: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:32,347 [Worker-1: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:32,349 [Worker-1: Updating Maven Project] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:32,952 [Worker-1: Updating Maven Project] INFO  o.e.m.j.i.AbstractJavaProjectConfigurator - Adding source folder /dev-c-yy-gz-aipoerp-bootstrap/src/main/java
2025-06-16 09:01:32,952 [Worker-1: Updating Maven Project] INFO  o.e.m.j.i.AbstractJavaProjectConfigurator - Adding resource folder /dev-c-yy-gz-aipoerp-bootstrap/src/main/resources
2025-06-16 09:01:32,954 [Worker-1: Updating Maven Project] INFO  o.e.m.j.i.AbstractJavaProjectConfigurator - Adding resource folder /dev-c-yy-gz-aipoerp-bootstrap/src/main/resources
2025-06-16 09:01:32,954 [Worker-1: Updating Maven Project] INFO  o.e.m.j.i.AbstractJavaProjectConfigurator - Adding source folder /dev-c-yy-gz-aipoerp-bootstrap/src/test/java
2025-06-16 09:01:32,955 [Worker-1: Updating Maven Project] INFO  o.e.m.j.i.AbstractJavaProjectConfigurator - Adding resource folder /dev-c-yy-gz-aipoerp-bootstrap/src/test/resources
2025-06-16 09:01:37,782 [Worker-1: Updating Maven Project] INFO  o.e.m.j.i.AbstractJavaProjectConfigurator - Adding source folder /dev-c-yy-gz-aipoerp-service/src/main/java
2025-06-16 09:01:37,783 [Worker-1: Updating Maven Project] INFO  o.e.m.j.i.AbstractJavaProjectConfigurator - Adding resource folder /dev-c-yy-gz-aipoerp-service/src/main/resources
2025-06-16 09:01:37,783 [Worker-1: Updating Maven Project] INFO  o.e.m.j.i.AbstractJavaProjectConfigurator - Adding resource folder /dev-c-yy-gz-aipoerp-service/src/main/resources
2025-06-16 09:01:37,783 [Worker-1: Updating Maven Project] INFO  o.e.m.j.i.AbstractJavaProjectConfigurator - Adding source folder /dev-c-yy-gz-aipoerp-service/src/test/java
2025-06-16 09:01:37,784 [Worker-1: Updating Maven Project] INFO  o.e.m.j.i.AbstractJavaProjectConfigurator - Adding resource folder /dev-c-yy-gz-aipoerp-service/src/test/resources
2025-06-16 09:01:38,092 [Worker-1: Updating Maven Project] INFO  o.e.m.w.i.f.ResourceFilteringBuildParticipant - Cleaning filtered folder for dev-c-yy-gz-aipoerp-bootstrap
2025-06-16 09:01:38,221 [Worker-1: Updating Maven Project] INFO  o.e.m.c.i.p.ProjectConfigurationManager - Update completed for 2 poms: local refresh takes 0.00 sec, refresh facades takes 2.00 sec, update config takes 5.00 sec 
2025-06-16 09:01:38,455 [Worker-12: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-16 09:01:38,455 [Worker-12: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-16 09:01:38,455 [Worker-12: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-api\src\main\resources
2025-06-16 09:01:38,455 [Worker-12: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-api\src\main\resources
2025-06-16 09:01:38,461 [Worker-12: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-16 09:01:38,461 [Worker-12: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-16 09:01:38,461 [Worker-12: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-api\src\test\resources
2025-06-16 09:01:38,463 [Worker-12: Building] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:38,464 [Worker-12: Building] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:38,799 [Worker-12: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-16 09:01:38,800 [Worker-12: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-16 09:01:38,800 [Worker-12: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-app\src\main\resources
2025-06-16 09:01:38,800 [Worker-12: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-app\src\main\resources
2025-06-16 09:01:38,803 [Worker-12: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-16 09:01:38,804 [Worker-12: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-16 09:01:38,804 [Worker-12: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-app\src\test\resources
2025-06-16 09:01:38,805 [Worker-12: Building] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:38,806 [Worker-12: Building] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:38,857 [Worker-12: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-16 09:01:38,857 [Worker-12: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-16 09:01:38,861 [Worker-12: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 13 resources
2025-06-16 09:01:38,966 [Worker-12: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 2 resources
2025-06-16 09:01:38,973 [Worker-12: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-16 09:01:38,973 [Worker-12: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-16 09:01:38,973 [Worker-12: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-bootstrap\src\test\resources
2025-06-16 09:01:38,974 [Worker-12: Building] INFO  o.e.m.w.i.f.ResourceFilteringBuildParticipant - Changed resources require a complete clean of filtered resources of dev-c-yy-gz-aipoerp-bootstrap
2025-06-16 09:01:38,976 [Worker-12: Building] INFO  o.e.m.w.i.f.ResourceFilteringBuildParticipant - Executing resource filtering for dev-c-yy-gz-aipoerp-bootstrap
2025-06-16 09:01:38,977 [Worker-12: Building] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:38,978 [Worker-12: Building] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:39,758 [Worker-12: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-16 09:01:39,758 [Worker-12: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-16 09:01:39,758 [Worker-12: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-infrastructure\src\main\resources
2025-06-16 09:01:39,758 [Worker-12: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-infrastructure\src\main\resources
2025-06-16 09:01:39,761 [Worker-12: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-16 09:01:39,761 [Worker-12: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-16 09:01:39,761 [Worker-12: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-infrastructure\src\test\resources
2025-06-16 09:01:39,762 [Worker-12: Building] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:39,763 [Worker-12: Building] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:39,812 [Worker-12: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-16 09:01:39,812 [Worker-12: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-16 09:01:39,813 [Worker-12: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-16 09:01:39,813 [Worker-12: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-16 09:01:39,815 [Worker-12: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-16 09:01:39,815 [Worker-12: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-16 09:01:39,815 [Worker-12: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-service\src\test\resources
2025-06-16 09:01:39,817 [Worker-12: Building] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:39,818 [Worker-12: Building] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:45,524 [Worker-30: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-16 09:01:45,525 [Worker-30: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-16 09:01:45,525 [Worker-30: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-api\src\main\resources
2025-06-16 09:01:45,525 [Worker-30: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-api\src\main\resources
2025-06-16 09:01:45,528 [Worker-30: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-16 09:01:45,528 [Worker-30: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-16 09:01:45,528 [Worker-30: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-api\src\test\resources
2025-06-16 09:01:45,530 [Worker-30: Building] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:45,531 [Worker-30: Building] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:47,651 [Worker-15: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-16 09:01:47,652 [Worker-15: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-16 09:01:47,652 [Worker-15: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-app\src\main\resources
2025-06-16 09:01:47,652 [Worker-15: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-app\src\main\resources
2025-06-16 09:01:47,656 [Worker-15: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-16 09:01:47,656 [Worker-15: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-16 09:01:47,656 [Worker-15: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-app\src\test\resources
2025-06-16 09:01:47,657 [Worker-15: Building] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:47,659 [Worker-15: Building] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:47,824 [Worker-15: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-16 09:01:47,825 [Worker-15: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-16 09:01:47,825 [Worker-15: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 13 resources
2025-06-16 09:01:47,844 [Worker-15: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 2 resources
2025-06-16 09:01:47,850 [Worker-15: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-16 09:01:47,850 [Worker-15: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-16 09:01:47,850 [Worker-15: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-bootstrap\src\test\resources
2025-06-16 09:01:47,852 [Worker-15: Building] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:47,852 [Worker-15: Building] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:47,957 [Worker-15: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-16 09:01:47,957 [Worker-15: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-16 09:01:47,957 [Worker-15: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-16 09:01:47,957 [Worker-15: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-16 09:01:47,960 [Worker-15: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-16 09:01:47,960 [Worker-15: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-16 09:01:47,960 [Worker-15: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-service\src\test\resources
2025-06-16 09:01:47,961 [Worker-15: Building] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-06-16 09:01:47,962 [Worker-15: Building] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-06-17 08:52:40,947 [Worker-6: Loading available Gradle versions] INFO  o.e.b.c.i.u.g.PublishedGradleVersions - Gradle version information cache is up-to-date. Trying to read.
2025-06-17 08:52:57,399 [Worker-5: Building] INFO  o.e.m.c.i.e.AbstractTransferListenerAdapter - Downloaded https://repo.yyrd.com/artifactory/yonyou-public/com/yonyou/iuap/iuap-2nd-party/8.0.0-SNAPSHOT/maven-metadata.xml
2025-06-17 08:52:57,402 [Worker-5: Building] WARN  o.e.m.c.i.embedder.EclipseLogger - Could not transfer metadata com.yonyou.iuap:iuap-2nd-party:8.0.0-SNAPSHOT/maven-metadata.xml from/to jfrog (https://repo.yyrd.com/artifactory/yonyou-public/): Connect to repo.yyrd.com:443 [repo.yyrd.com/*************] failed: Connect timed out
2025-06-17 08:53:07,436 [Worker-5: Building] INFO  o.e.m.c.i.e.AbstractTransferListenerAdapter - Downloaded https://repo.yyrd.com/artifactory/yonyou-public/com/yonyou/iuap/iuap-3rd-party/8.0.0-SNAPSHOT/maven-metadata.xml
2025-06-17 08:53:07,437 [Worker-5: Building] WARN  o.e.m.c.i.embedder.EclipseLogger - Could not transfer metadata com.yonyou.iuap:iuap-3rd-party:8.0.0-SNAPSHOT/maven-metadata.xml from/to jfrog (https://repo.yyrd.com/artifactory/yonyou-public/): Connect to repo.yyrd.com:443 [repo.yyrd.com/*************] failed: Connect timed out
2025-06-17 08:53:17,575 [Worker-5: Building] INFO  o.e.m.c.i.e.AbstractTransferListenerAdapter - Downloaded https://repo.yyrd.com/artifactory/yonyou-public/com/yonyou/iuap/iuap-2nd-party/8.1.0-SNAPSHOT/maven-metadata.xml
2025-06-17 08:53:17,576 [Worker-5: Building] WARN  o.e.m.c.i.embedder.EclipseLogger - Could not transfer metadata com.yonyou.iuap:iuap-2nd-party:8.1.0-SNAPSHOT/maven-metadata.xml from/to jfrog (https://repo.yyrd.com/artifactory/yonyou-public/): Connect to repo.yyrd.com:443 [repo.yyrd.com/*************] failed: Connect timed out
2025-06-17 08:53:27,772 [Worker-5: Building] INFO  o.e.m.c.i.e.AbstractTransferListenerAdapter - Downloaded https://repo.yyrd.com/artifactory/yonyou-public/com/yonyou/iuap/iuap-3rd-party/8.1.0-SNAPSHOT/maven-metadata.xml
2025-06-17 08:53:27,773 [Worker-5: Building] WARN  o.e.m.c.i.embedder.EclipseLogger - Could not transfer metadata com.yonyou.iuap:iuap-3rd-party:8.1.0-SNAPSHOT/maven-metadata.xml from/to jfrog (https://repo.yyrd.com/artifactory/yonyou-public/): Connect to repo.yyrd.com:443 [repo.yyrd.com/*************] failed: Connect timed out
2025-06-17 08:53:28,684 [Worker-5: Building] INFO  o.e.m.c.i.p.WorkspaceClassifierResolverManager - Resolving P/dev-c-yy-gz-aipoerp-api: com.yonyou.ucf:c-yy-gz-aipoerp-api:ddm-3.0-RELEASE with classifier  to /dev-c-yy-gz-aipoerp-api/target/classes
2025-06-17 08:53:28,686 [Worker-5: Building] INFO  o.e.m.c.i.p.WorkspaceClassifierResolverManager - Resolving P/dev-c-yy-gz-aipoerp-app: com.yonyou.ucf:c-yy-gz-aipoerp-app:ddm-3.0-RELEASE with classifier  to /dev-c-yy-gz-aipoerp-app/target/classes
2025-06-17 08:53:28,686 [Worker-5: Building] INFO  o.e.m.c.i.p.WorkspaceClassifierResolverManager - Resolving P/dev-c-yy-gz-aipoerp-infrastructure: com.yonyou.ucf:c-yy-gz-aipoerp-infrastructure:ddm-3.0-RELEASE with classifier  to /dev-c-yy-gz-aipoerp-infrastructure/target/classes
2025-06-17 08:53:28,764 [Worker-5: Building] INFO  o.e.m.c.i.p.WorkspaceClassifierResolverManager - Resolving P/dev-c-yy-gz-aipoerp-service: com.yonyou.ucf:c-yy-gz-aipoerp-service:ddm-3.0-RELEASE with classifier  to /dev-c-yy-gz-aipoerp-service/target/classes
2025-06-17 08:53:29,079 [Worker-5: Building] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-06-17 08:53:29,085 [Worker-5: Building] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-06-18 14:46:45,628 [Worker-1: Loading available Gradle versions] INFO  o.e.b.c.i.u.g.PublishedGradleVersions - Gradle version information cache is out-of-date. Trying to update.
2025-06-18 14:47:10,731 [Worker-29: Building] INFO  o.e.m.c.i.p.WorkspaceClassifierResolverManager - Resolving P/dev-c-yy-gz-aipoerp-api: com.yonyou.ucf:c-yy-gz-aipoerp-api:ddm-3.0-RELEASE with classifier  to /dev-c-yy-gz-aipoerp-api/target/classes
2025-06-18 14:47:10,735 [Worker-29: Building] INFO  o.e.m.c.i.p.WorkspaceClassifierResolverManager - Resolving P/dev-c-yy-gz-aipoerp-app: com.yonyou.ucf:c-yy-gz-aipoerp-app:ddm-3.0-RELEASE with classifier  to /dev-c-yy-gz-aipoerp-app/target/classes
2025-06-18 14:47:10,736 [Worker-29: Building] INFO  o.e.m.c.i.p.WorkspaceClassifierResolverManager - Resolving P/dev-c-yy-gz-aipoerp-infrastructure: com.yonyou.ucf:c-yy-gz-aipoerp-infrastructure:ddm-3.0-RELEASE with classifier  to /dev-c-yy-gz-aipoerp-infrastructure/target/classes
2025-06-18 14:47:10,816 [Worker-29: Building] INFO  o.e.m.c.i.p.WorkspaceClassifierResolverManager - Resolving P/dev-c-yy-gz-aipoerp-service: com.yonyou.ucf:c-yy-gz-aipoerp-service:ddm-3.0-RELEASE with classifier  to /dev-c-yy-gz-aipoerp-service/target/classes
2025-06-18 14:47:11,067 [Worker-29: Building] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-06-18 14:47:11,072 [Worker-29: Building] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-06-18 15:19:09,821 [Worker-46: Building] INFO  o.e.m.c.i.p.WorkspaceClassifierResolverManager - Resolving P/dev-c-yy-gz-aipoerp-api: com.yonyou.ucf:c-yy-gz-aipoerp-api:ddm-3.0-RELEASE with classifier  to /dev-c-yy-gz-aipoerp-api/target/classes
2025-06-18 15:19:09,822 [Worker-46: Building] INFO  o.e.m.c.i.p.WorkspaceClassifierResolverManager - Resolving P/dev-c-yy-gz-aipoerp-infrastructure: com.yonyou.ucf:c-yy-gz-aipoerp-infrastructure:ddm-3.0-RELEASE with classifier  to /dev-c-yy-gz-aipoerp-infrastructure/target/classes
2025-06-18 15:19:09,851 [Worker-46: Building] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-06-18 15:19:09,853 [Worker-46: Building] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-06-18 15:19:10,204 [Worker-46: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-18 15:19:10,204 [Worker-46: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-18 15:19:10,209 [Worker-46: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-18 15:19:10,210 [Worker-46: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-18 15:19:10,217 [Worker-46: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-18 15:19:10,217 [Worker-46: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-18 15:19:10,217 [Worker-46: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-service\src\test\resources
2025-06-18 15:19:10,223 [Worker-46: Building] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-06-18 15:19:10,224 [Worker-46: Building] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-06-18 15:19:22,602 [Worker-48: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-18 15:19:22,603 [Worker-48: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-18 15:19:22,604 [Worker-48: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-18 15:19:22,605 [Worker-48: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-18 15:19:22,611 [Worker-48: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-18 15:19:22,611 [Worker-48: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-18 15:19:22,611 [Worker-48: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-service\src\test\resources
2025-06-18 15:19:41,224 [Worker-48: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-18 15:19:41,224 [Worker-48: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-18 15:19:41,224 [Worker-48: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-18 15:19:41,225 [Worker-48: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-18 15:19:41,229 [Worker-48: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-18 15:19:41,229 [Worker-48: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-18 15:19:41,229 [Worker-48: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-service\src\test\resources
2025-06-18 15:21:21,998 [Worker-44: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-18 15:21:21,998 [Worker-44: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-18 15:21:21,999 [Worker-44: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-18 15:21:21,999 [Worker-44: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-18 15:21:22,003 [Worker-44: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-18 15:21:22,003 [Worker-44: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-18 15:21:22,003 [Worker-44: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-service\src\test\resources
2025-06-18 15:21:30,813 [Worker-49: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-18 15:21:30,813 [Worker-49: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-18 15:21:30,814 [Worker-49: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-18 15:21:30,814 [Worker-49: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-18 15:21:30,818 [Worker-49: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-18 15:21:30,818 [Worker-49: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-18 15:21:30,818 [Worker-49: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-service\src\test\resources
2025-06-18 15:21:48,795 [Worker-50: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-18 15:21:48,795 [Worker-50: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-18 15:21:48,796 [Worker-50: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-18 15:21:48,796 [Worker-50: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-18 15:21:48,801 [Worker-50: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-18 15:21:48,802 [Worker-50: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-18 15:21:48,802 [Worker-50: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-service\src\test\resources
2025-06-18 15:21:57,523 [Worker-47: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-18 15:21:57,524 [Worker-47: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-18 15:21:57,524 [Worker-47: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-18 15:21:57,524 [Worker-47: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-18 15:21:57,527 [Worker-47: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-18 15:21:57,527 [Worker-47: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-18 15:21:57,527 [Worker-47: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-service\src\test\resources
2025-06-18 15:22:08,096 [Worker-51: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-18 15:22:08,096 [Worker-51: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-18 15:22:08,098 [Worker-51: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-18 15:22:08,098 [Worker-51: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-18 15:22:08,101 [Worker-51: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-18 15:22:08,101 [Worker-51: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-18 15:22:08,101 [Worker-51: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-service\src\test\resources
2025-06-18 15:32:15,874 [Worker-45: Push to aipoerp refs/heads/prd - origin] INFO  o.a.s.c.i.DefaultIoServiceFactoryFactory - No detected/configured IoServiceFactoryFactory; using Nio2ServiceFactoryFactory
2025-06-18 15:32:18,207 [sshd-JGitSshClient[1230f40]-nio2-thread-6] WARN  o.e.j.i.t.sshd.JGitClientSession - exceptionCaught(JGitClientSession[******************/*************:22])[state=Opened] SshException: Server key did not validate
2025-06-18 15:32:18,210 [sshd-JGitSshClient[1230f40]-nio2-thread-6] INFO  o.e.j.i.t.sshd.JGitClientSession - Disconnecting(JGitClientSession[******************/*************:22]): SSH2_DISCONNECT_HOST_KEY_NOT_VERIFIABLE - Server key did not validate
2025-06-18 15:33:00,454 [sshd-JGitSshClient[66bdafb1]-nio2-thread-6] WARN  o.e.j.i.t.sshd.JGitClientSession - exceptionCaught(JGitClientSession[******************/*************:22])[state=Opened] SshException: Server key did not validate
2025-06-18 15:33:00,455 [sshd-JGitSshClient[66bdafb1]-nio2-thread-6] INFO  o.e.j.i.t.sshd.JGitClientSession - Disconnecting(JGitClientSession[******************/*************:22]): SSH2_DISCONNECT_HOST_KEY_NOT_VERIFIABLE - Server key did not validate
2025-06-18 15:33:03,635 [sshd-JGitSshClient[9d6c4a1]-nio2-thread-6] WARN  o.e.j.i.t.sshd.JGitClientSession - exceptionCaught(JGitClientSession[******************/*************:22])[state=Opened] SshException: Server key did not validate
2025-06-18 15:33:03,637 [sshd-JGitSshClient[9d6c4a1]-nio2-thread-6] INFO  o.e.j.i.t.sshd.JGitClientSession - Disconnecting(JGitClientSession[******************/*************:22]): SSH2_DISCONNECT_HOST_KEY_NOT_VERIFIABLE - Server key did not validate
2025-06-18 15:49:45,823 [Worker-61: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-18 15:49:45,823 [Worker-61: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-18 15:49:45,823 [Worker-61: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-18 15:49:45,823 [Worker-61: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-18 15:49:45,827 [Worker-61: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-18 15:49:45,827 [Worker-61: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-18 15:49:45,827 [Worker-61: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-service\src\test\resources
2025-06-18 15:51:15,920 [Worker-60: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-18 15:51:15,920 [Worker-60: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-18 15:51:15,920 [Worker-60: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-18 15:51:15,921 [Worker-60: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-18 15:51:15,925 [Worker-60: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-18 15:51:15,925 [Worker-60: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-18 15:51:15,925 [Worker-60: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-service\src\test\resources
2025-06-19 10:01:50,528 [Worker-4: Loading available Gradle versions] INFO  o.e.b.c.i.u.g.PublishedGradleVersions - Gradle version information cache is up-to-date. Trying to read.
2025-06-19 10:02:07,323 [Worker-3: Building] INFO  o.e.m.c.i.e.AbstractTransferListenerAdapter - Downloaded https://repo.yyrd.com/artifactory/yonyou-public/com/yonyou/iuap/iuap-2nd-party/8.0.0-SNAPSHOT/maven-metadata.xml
2025-06-19 10:02:07,326 [Worker-3: Building] WARN  o.e.m.c.i.embedder.EclipseLogger - Could not transfer metadata com.yonyou.iuap:iuap-2nd-party:8.0.0-SNAPSHOT/maven-metadata.xml from/to jfrog (https://repo.yyrd.com/artifactory/yonyou-public/): Connect to repo.yyrd.com:443 [repo.yyrd.com/*************] failed: Connect timed out
2025-06-19 10:02:17,340 [Worker-3: Building] INFO  o.e.m.c.i.e.AbstractTransferListenerAdapter - Downloaded https://repo.yyrd.com/artifactory/yonyou-public/com/yonyou/iuap/iuap-3rd-party/8.0.0-SNAPSHOT/maven-metadata.xml
2025-06-19 10:02:17,341 [Worker-3: Building] WARN  o.e.m.c.i.embedder.EclipseLogger - Could not transfer metadata com.yonyou.iuap:iuap-3rd-party:8.0.0-SNAPSHOT/maven-metadata.xml from/to jfrog (https://repo.yyrd.com/artifactory/yonyou-public/): Connect to repo.yyrd.com:443 [repo.yyrd.com/*************] failed: Connect timed out
2025-06-19 10:02:27,490 [Worker-3: Building] INFO  o.e.m.c.i.e.AbstractTransferListenerAdapter - Downloaded https://repo.yyrd.com/artifactory/yonyou-public/com/yonyou/iuap/iuap-2nd-party/8.1.0-SNAPSHOT/maven-metadata.xml
2025-06-19 10:02:27,491 [Worker-3: Building] WARN  o.e.m.c.i.embedder.EclipseLogger - Could not transfer metadata com.yonyou.iuap:iuap-2nd-party:8.1.0-SNAPSHOT/maven-metadata.xml from/to jfrog (https://repo.yyrd.com/artifactory/yonyou-public/): Connect to repo.yyrd.com:443 [repo.yyrd.com/*************] failed: Connect timed out
2025-06-19 10:02:37,507 [Worker-3: Building] INFO  o.e.m.c.i.e.AbstractTransferListenerAdapter - Downloaded https://repo.yyrd.com/artifactory/yonyou-public/com/yonyou/iuap/iuap-3rd-party/8.1.0-SNAPSHOT/maven-metadata.xml
2025-06-19 10:02:37,509 [Worker-3: Building] WARN  o.e.m.c.i.embedder.EclipseLogger - Could not transfer metadata com.yonyou.iuap:iuap-3rd-party:8.1.0-SNAPSHOT/maven-metadata.xml from/to jfrog (https://repo.yyrd.com/artifactory/yonyou-public/): Connect to repo.yyrd.com:443 [repo.yyrd.com/*************] failed: Connect timed out
2025-06-19 10:02:38,611 [Worker-3: Building] INFO  o.e.m.c.i.p.WorkspaceClassifierResolverManager - Resolving P/dev-c-yy-gz-aipoerp-api: com.yonyou.ucf:c-yy-gz-aipoerp-api:ddm-3.0-RELEASE with classifier  to /dev-c-yy-gz-aipoerp-api/target/classes
2025-06-19 10:02:38,613 [Worker-3: Building] INFO  o.e.m.c.i.p.WorkspaceClassifierResolverManager - Resolving P/dev-c-yy-gz-aipoerp-app: com.yonyou.ucf:c-yy-gz-aipoerp-app:ddm-3.0-RELEASE with classifier  to /dev-c-yy-gz-aipoerp-app/target/classes
2025-06-19 10:02:38,613 [Worker-3: Building] INFO  o.e.m.c.i.p.WorkspaceClassifierResolverManager - Resolving P/dev-c-yy-gz-aipoerp-infrastructure: com.yonyou.ucf:c-yy-gz-aipoerp-infrastructure:ddm-3.0-RELEASE with classifier  to /dev-c-yy-gz-aipoerp-infrastructure/target/classes
2025-06-19 10:02:38,691 [Worker-3: Building] INFO  o.e.m.c.i.p.WorkspaceClassifierResolverManager - Resolving P/dev-c-yy-gz-aipoerp-service: com.yonyou.ucf:c-yy-gz-aipoerp-service:ddm-3.0-RELEASE with classifier  to /dev-c-yy-gz-aipoerp-service/target/classes
2025-06-19 10:02:38,942 [Worker-3: Building] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-06-19 10:02:38,947 [Worker-3: Building] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-06-19 21:07:42,207 [Worker-109: Launching MDFApplication] INFO  o.e.m.c.i.p.WorkspaceClassifierResolverManager - Resolving P/dev-c-yy-gz-aipoerp-api: com.yonyou.ucf:c-yy-gz-aipoerp-api:ddm-3.0-RELEASE with classifier null to /dev-c-yy-gz-aipoerp-api/target/classes
2025-06-19 21:07:42,208 [Worker-109: Launching MDFApplication] INFO  o.e.m.c.i.p.WorkspaceClassifierResolverManager - Resolving P/dev-c-yy-gz-aipoerp-app: com.yonyou.ucf:c-yy-gz-aipoerp-app:ddm-3.0-RELEASE with classifier null to /dev-c-yy-gz-aipoerp-app/target/classes
2025-06-19 21:07:42,209 [Worker-109: Launching MDFApplication] INFO  o.e.m.c.i.p.WorkspaceClassifierResolverManager - Resolving P/dev-c-yy-gz-aipoerp-infrastructure: com.yonyou.ucf:c-yy-gz-aipoerp-infrastructure:ddm-3.0-RELEASE with classifier null to /dev-c-yy-gz-aipoerp-infrastructure/target/classes
2025-06-19 21:07:42,209 [Worker-109: Launching MDFApplication] INFO  o.e.m.c.i.p.WorkspaceClassifierResolverManager - Resolving P/dev-c-yy-gz-aipoerp-service: com.yonyou.ucf:c-yy-gz-aipoerp-service:ddm-3.0-RELEASE with classifier null to /dev-c-yy-gz-aipoerp-service/target/classes
2025-06-20 10:18:06,572 [Worker-5: Loading available Gradle versions] INFO  o.e.b.c.i.u.g.PublishedGradleVersions - Gradle version information cache is out-of-date. Trying to update.
2025-06-20 10:29:01,812 [Worker-41: Building] INFO  o.e.m.c.i.e.AbstractTransferListenerAdapter - Downloading https://repo.yyrd.com/artifactory/yonyou-public/com/yonyou/iuap/iuap-2nd-party/8.0.0-SNAPSHOT/maven-metadata.xml
2025-06-20 10:29:01,827 [Worker-41: Building] INFO  o.e.m.c.i.e.AbstractTransferListenerAdapter - Downloaded https://repo.yyrd.com/artifactory/yonyou-public/com/yonyou/iuap/iuap-2nd-party/8.0.0-SNAPSHOT/maven-metadata.xml
2025-06-20 10:29:01,903 [Worker-41: Building] INFO  o.e.m.c.i.e.AbstractTransferListenerAdapter - Downloading https://repo.yyrd.com/artifactory/yonyou-public/com/yonyou/iuap/iuap-3rd-party/8.0.0-SNAPSHOT/maven-metadata.xml
2025-06-20 10:29:01,906 [Worker-41: Building] INFO  o.e.m.c.i.e.AbstractTransferListenerAdapter - Downloaded https://repo.yyrd.com/artifactory/yonyou-public/com/yonyou/iuap/iuap-3rd-party/8.0.0-SNAPSHOT/maven-metadata.xml
2025-06-20 10:29:02,082 [Worker-41: Building] INFO  o.e.m.c.i.e.AbstractTransferListenerAdapter - Downloading https://repo.yyrd.com/artifactory/yonyou-public/com/yonyou/iuap/iuap-2nd-party/8.1.0-SNAPSHOT/maven-metadata.xml
2025-06-20 10:29:02,086 [Worker-41: Building] INFO  o.e.m.c.i.e.AbstractTransferListenerAdapter - Downloaded https://repo.yyrd.com/artifactory/yonyou-public/com/yonyou/iuap/iuap-2nd-party/8.1.0-SNAPSHOT/maven-metadata.xml
2025-06-20 10:29:02,174 [Worker-41: Building] INFO  o.e.m.c.i.e.AbstractTransferListenerAdapter - Downloading https://repo.yyrd.com/artifactory/yonyou-public/com/yonyou/iuap/iuap-3rd-party/8.1.0-SNAPSHOT/maven-metadata.xml
2025-06-20 10:29:02,178 [Worker-41: Building] INFO  o.e.m.c.i.e.AbstractTransferListenerAdapter - Downloaded https://repo.yyrd.com/artifactory/yonyou-public/com/yonyou/iuap/iuap-3rd-party/8.1.0-SNAPSHOT/maven-metadata.xml
2025-06-20 10:29:03,267 [Worker-41: Building] INFO  o.e.m.c.i.p.WorkspaceClassifierResolverManager - Resolving P/dev-c-yy-gz-aipoerp-api: com.yonyou.ucf:c-yy-gz-aipoerp-api:ddm-3.0-RELEASE with classifier  to /dev-c-yy-gz-aipoerp-api/target/classes
2025-06-20 10:29:03,269 [Worker-41: Building] INFO  o.e.m.c.i.p.WorkspaceClassifierResolverManager - Resolving P/dev-c-yy-gz-aipoerp-infrastructure: com.yonyou.ucf:c-yy-gz-aipoerp-infrastructure:ddm-3.0-RELEASE with classifier  to /dev-c-yy-gz-aipoerp-infrastructure/target/classes
2025-06-20 10:29:03,489 [Worker-41: Building] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-06-20 10:29:03,495 [Worker-41: Building] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-06-20 10:29:03,913 [Worker-41: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-20 10:29:03,913 [Worker-41: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-20 10:29:03,917 [Worker-41: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-20 10:29:03,918 [Worker-41: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-20 10:29:03,924 [Worker-41: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-20 10:29:03,924 [Worker-41: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-20 10:29:03,924 [Worker-41: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-service\src\test\resources
2025-06-20 10:29:03,928 [Worker-41: Building] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-06-20 10:29:03,929 [Worker-41: Building] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-06-20 10:29:06,941 [Worker-39: Building] INFO  o.e.m.c.i.p.WorkspaceClassifierResolverManager - Resolving P/dev-c-yy-gz-aipoerp-api: com.yonyou.ucf:c-yy-gz-aipoerp-api:ddm-3.0-RELEASE with classifier  to /dev-c-yy-gz-aipoerp-api/target/classes
2025-06-20 10:29:06,942 [Worker-39: Building] INFO  o.e.m.c.i.p.WorkspaceClassifierResolverManager - Resolving P/dev-c-yy-gz-aipoerp-app: com.yonyou.ucf:c-yy-gz-aipoerp-app:ddm-3.0-RELEASE with classifier  to /dev-c-yy-gz-aipoerp-app/target/classes
2025-06-20 10:29:06,942 [Worker-39: Building] INFO  o.e.m.c.i.p.WorkspaceClassifierResolverManager - Resolving P/dev-c-yy-gz-aipoerp-infrastructure: com.yonyou.ucf:c-yy-gz-aipoerp-infrastructure:ddm-3.0-RELEASE with classifier  to /dev-c-yy-gz-aipoerp-infrastructure/target/classes
2025-06-20 10:29:07,020 [Worker-39: Building] INFO  o.e.m.c.i.p.WorkspaceClassifierResolverManager - Resolving P/dev-c-yy-gz-aipoerp-service: com.yonyou.ucf:c-yy-gz-aipoerp-service:ddm-3.0-RELEASE with classifier  to /dev-c-yy-gz-aipoerp-service/target/classes
2025-06-20 10:29:07,128 [Worker-39: Building] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-06-20 10:29:07,130 [Worker-39: Building] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-06-20 10:29:39,471 [Worker-35: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-20 10:29:39,471 [Worker-35: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-20 10:29:39,473 [Worker-35: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-20 10:29:39,473 [Worker-35: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-20 10:29:39,477 [Worker-35: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-20 10:29:39,478 [Worker-35: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-20 10:29:39,478 [Worker-35: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-service\src\test\resources
2025-06-20 10:29:58,281 [Worker-40: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-20 10:29:58,281 [Worker-40: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-20 10:29:58,281 [Worker-40: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-20 10:29:58,281 [Worker-40: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-20 10:29:58,284 [Worker-40: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-20 10:29:58,284 [Worker-40: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-20 10:29:58,284 [Worker-40: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-service\src\test\resources
2025-06-20 10:30:04,864 [Worker-39: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-20 10:30:04,864 [Worker-39: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-20 10:30:04,865 [Worker-39: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-20 10:30:04,865 [Worker-39: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-20 10:30:04,868 [Worker-39: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-20 10:30:04,869 [Worker-39: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-20 10:30:04,869 [Worker-39: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-service\src\test\resources
2025-06-20 10:30:22,286 [Worker-9: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-20 10:30:22,286 [Worker-9: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-20 10:30:22,286 [Worker-9: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-20 10:30:22,287 [Worker-9: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-20 10:30:22,290 [Worker-9: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-20 10:30:22,290 [Worker-9: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-20 10:30:22,290 [Worker-9: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-service\src\test\resources
2025-06-20 10:30:24,540 [Worker-42: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-20 10:30:24,540 [Worker-42: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-20 10:30:24,540 [Worker-42: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-20 10:30:24,540 [Worker-42: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-20 10:30:24,543 [Worker-42: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-20 10:30:24,543 [Worker-42: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-20 10:30:24,544 [Worker-42: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-service\src\test\resources
2025-06-20 10:30:50,400 [Worker-39: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-20 10:30:50,400 [Worker-39: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-20 10:30:50,401 [Worker-39: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-20 10:30:50,401 [Worker-39: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-20 10:30:50,405 [Worker-39: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-20 10:30:50,405 [Worker-39: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-20 10:30:50,405 [Worker-39: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-service\src\test\resources
2025-06-20 10:31:17,409 [Worker-42: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-20 10:31:17,410 [Worker-42: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-20 10:31:17,410 [Worker-42: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-20 10:31:17,410 [Worker-42: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-20 10:31:17,414 [Worker-42: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-20 10:31:17,414 [Worker-42: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-20 10:31:17,414 [Worker-42: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-service\src\test\resources
2025-06-20 10:31:25,818 [Worker-42: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-20 10:31:25,818 [Worker-42: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-20 10:31:25,818 [Worker-42: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-20 10:31:25,818 [Worker-42: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-20 10:31:25,821 [Worker-42: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-20 10:31:25,821 [Worker-42: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-20 10:31:25,821 [Worker-42: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-service\src\test\resources
2025-06-20 10:31:53,699 [Worker-40: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-20 10:31:53,699 [Worker-40: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-20 10:31:53,699 [Worker-40: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-20 10:31:53,700 [Worker-40: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-20 10:31:53,702 [Worker-40: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-20 10:31:53,703 [Worker-40: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-20 10:31:53,703 [Worker-40: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-service\src\test\resources
2025-06-20 10:32:58,012 [Worker-42: Push to aipoerp refs/heads/prd - origin] INFO  o.a.s.c.i.DefaultIoServiceFactoryFactory - No detected/configured IoServiceFactoryFactory; using Nio2ServiceFactoryFactory
2025-06-20 15:04:17,259 [Worker-50: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-20 15:04:17,261 [Worker-50: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-20 15:04:17,262 [Worker-50: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 13 resources
2025-06-20 15:04:17,769 [Worker-50: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 2 resources
2025-06-20 15:04:17,775 [Worker-50: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-20 15:04:17,776 [Worker-50: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-20 15:04:17,776 [Worker-50: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-bootstrap\src\test\resources
2025-06-20 15:04:17,856 [Worker-50: Building] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-06-20 15:04:17,861 [Worker-50: Building] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-06-20 16:11:28,123 [Worker-41: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-20 16:11:28,123 [Worker-41: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-20 16:11:28,123 [Worker-41: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-20 16:11:28,124 [Worker-41: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-20 16:11:28,126 [Worker-41: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-20 16:11:28,126 [Worker-41: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-20 16:11:28,126 [Worker-41: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-service\src\test\resources
2025-06-20 17:15:13,639 [Worker-54: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-20 17:15:13,641 [Worker-54: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-20 17:15:13,642 [Worker-54: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-20 17:15:13,642 [Worker-54: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-20 17:15:13,646 [Worker-54: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-20 17:15:13,646 [Worker-54: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-20 17:15:13,646 [Worker-54: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-service\src\test\resources
2025-06-20 17:15:49,653 [Worker-50: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-20 17:15:49,653 [Worker-50: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-20 17:15:49,653 [Worker-50: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-20 17:15:49,653 [Worker-50: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-20 17:15:49,656 [Worker-50: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-20 17:15:49,656 [Worker-50: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-20 17:15:49,656 [Worker-50: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-service\src\test\resources
2025-06-20 17:16:10,345 [Worker-58: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-20 17:16:10,345 [Worker-58: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-20 17:16:10,345 [Worker-58: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-20 17:16:10,345 [Worker-58: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-20 17:16:10,348 [Worker-58: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-20 17:16:10,349 [Worker-58: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-20 17:16:10,349 [Worker-58: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-service\src\test\resources
2025-06-20 17:16:18,774 [Worker-57: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-20 17:16:18,774 [Worker-57: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-20 17:16:18,774 [Worker-57: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-20 17:16:18,774 [Worker-57: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-20 17:16:18,778 [Worker-57: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-20 17:16:18,778 [Worker-57: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-20 17:16:18,778 [Worker-57: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-service\src\test\resources
2025-06-20 17:17:00,370 [Worker-58: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-20 17:17:00,370 [Worker-58: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-20 17:17:00,370 [Worker-58: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-20 17:17:00,370 [Worker-58: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-20 17:17:00,373 [Worker-58: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-20 17:17:00,373 [Worker-58: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-20 17:17:00,373 [Worker-58: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-service\src\test\resources
2025-06-20 17:17:32,839 [Worker-59: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-20 17:17:32,839 [Worker-59: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-20 17:17:32,839 [Worker-59: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-20 17:17:32,840 [Worker-59: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-20 17:17:32,843 [Worker-59: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-20 17:17:32,843 [Worker-59: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-20 17:17:32,843 [Worker-59: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-service\src\test\resources
2025-06-20 17:19:12,152 [Worker-59: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-20 17:19:12,153 [Worker-59: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-20 17:19:12,153 [Worker-59: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-20 17:19:12,153 [Worker-59: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-20 17:19:12,156 [Worker-59: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-20 17:19:12,156 [Worker-59: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-20 17:19:12,156 [Worker-59: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-service\src\test\resources
2025-06-20 17:19:14,395 [Worker-58: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-20 17:19:14,395 [Worker-58: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-20 17:19:14,397 [Worker-58: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-20 17:19:14,397 [Worker-58: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-20 17:19:14,400 [Worker-58: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-20 17:19:14,400 [Worker-58: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-20 17:19:14,400 [Worker-58: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-service\src\test\resources
2025-06-20 17:21:07,605 [Worker-54: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-20 17:21:07,605 [Worker-54: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-20 17:21:07,605 [Worker-54: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-20 17:21:07,605 [Worker-54: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-20 17:21:07,607 [Worker-54: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-20 17:21:07,607 [Worker-54: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-20 17:21:07,607 [Worker-54: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-service\src\test\resources
2025-06-20 17:21:17,585 [Worker-58: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-20 17:21:17,587 [Worker-58: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-20 17:21:17,587 [Worker-58: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-20 17:21:17,587 [Worker-58: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-20 17:21:17,590 [Worker-58: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-20 17:21:17,591 [Worker-58: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-20 17:21:17,591 [Worker-58: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-service\src\test\resources
2025-06-20 17:34:17,774 [Worker-50: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-20 17:34:17,774 [Worker-50: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-20 17:34:17,774 [Worker-50: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-20 17:34:17,775 [Worker-50: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-20 17:34:17,778 [Worker-50: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-20 17:34:17,778 [Worker-50: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-20 17:34:17,778 [Worker-50: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-service\src\test\resources
2025-06-20 17:39:26,266 [Worker-58: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-20 17:39:26,266 [Worker-58: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-20 17:39:26,266 [Worker-58: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-20 17:39:26,267 [Worker-58: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-20 17:39:26,269 [Worker-58: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-20 17:39:26,269 [Worker-58: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-20 17:39:26,269 [Worker-58: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-service\src\test\resources
2025-06-20 17:41:18,981 [Worker-58: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-20 17:41:18,981 [Worker-58: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-20 17:41:18,981 [Worker-58: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-20 17:41:18,982 [Worker-58: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-20 17:41:18,984 [Worker-58: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-20 17:41:18,985 [Worker-58: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-20 17:41:18,985 [Worker-58: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-service\src\test\resources
2025-06-20 17:43:31,642 [Worker-63: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-20 17:43:31,642 [Worker-63: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-20 17:43:31,642 [Worker-63: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-20 17:43:31,642 [Worker-63: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-20 17:43:31,646 [Worker-63: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-20 17:43:31,646 [Worker-63: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-20 17:43:31,646 [Worker-63: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-service\src\test\resources
2025-06-20 17:44:06,157 [Worker-62: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-20 17:44:06,157 [Worker-62: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-20 17:44:06,157 [Worker-62: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-20 17:44:06,158 [Worker-62: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-20 17:44:06,161 [Worker-62: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-20 17:44:06,161 [Worker-62: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-20 17:44:06,161 [Worker-62: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-service\src\test\resources
2025-06-20 17:45:31,559 [Worker-54: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-20 17:45:31,559 [Worker-54: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-20 17:45:31,559 [Worker-54: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-20 17:45:31,560 [Worker-54: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-20 17:45:31,563 [Worker-54: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-20 17:45:31,563 [Worker-54: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-20 17:45:31,563 [Worker-54: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-service\src\test\resources
2025-06-20 17:45:43,795 [Worker-66: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-20 17:45:43,795 [Worker-66: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-20 17:45:43,795 [Worker-66: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-20 17:45:43,795 [Worker-66: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-20 17:45:43,799 [Worker-66: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-20 17:45:43,799 [Worker-66: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-20 17:45:43,799 [Worker-66: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-service\src\test\resources
2025-06-20 17:46:28,382 [Worker-64: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-20 17:46:28,382 [Worker-64: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-20 17:46:28,383 [Worker-64: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-20 17:46:28,383 [Worker-64: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-20 17:46:28,386 [Worker-64: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-20 17:46:28,386 [Worker-64: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-20 17:46:28,386 [Worker-64: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-service\src\test\resources
2025-06-20 17:47:06,204 [Worker-58: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-20 17:47:06,204 [Worker-58: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-20 17:47:06,205 [Worker-58: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-20 17:47:06,205 [Worker-58: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-20 17:47:06,208 [Worker-58: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-20 17:47:06,208 [Worker-58: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-20 17:47:06,208 [Worker-58: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-service\src\test\resources
2025-06-20 17:47:13,753 [Worker-58: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-20 17:47:13,753 [Worker-58: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-20 17:47:13,754 [Worker-58: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-20 17:47:13,754 [Worker-58: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-20 17:47:13,756 [Worker-58: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-20 17:47:13,756 [Worker-58: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-20 17:47:13,756 [Worker-58: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-service\src\test\resources
2025-06-20 17:47:55,752 [Worker-63: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-20 17:47:55,752 [Worker-63: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-20 17:47:55,753 [Worker-63: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-20 17:47:55,753 [Worker-63: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-20 17:47:55,756 [Worker-63: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-20 17:47:55,756 [Worker-63: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-20 17:47:55,756 [Worker-63: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-service\src\test\resources
2025-06-20 17:48:37,450 [Worker-64: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-20 17:48:37,450 [Worker-64: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-20 17:48:37,451 [Worker-64: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-20 17:48:37,451 [Worker-64: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-20 17:48:37,455 [Worker-64: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-20 17:48:37,455 [Worker-64: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-20 17:48:37,456 [Worker-64: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-service\src\test\resources
2025-06-20 17:48:46,581 [Worker-50: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-20 17:48:46,581 [Worker-50: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-20 17:48:46,581 [Worker-50: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-20 17:48:46,581 [Worker-50: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-20 17:48:46,584 [Worker-50: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-20 17:48:46,584 [Worker-50: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-20 17:48:46,584 [Worker-50: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-service\src\test\resources
2025-06-20 17:49:25,653 [Worker-58: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-20 17:49:25,653 [Worker-58: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-20 17:49:25,653 [Worker-58: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-20 17:49:25,654 [Worker-58: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-20 17:49:25,661 [Worker-58: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-20 17:49:25,661 [Worker-58: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-20 17:49:25,661 [Worker-58: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-service\src\test\resources
2025-06-20 17:51:15,390 [Worker-54: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-20 17:51:15,390 [Worker-54: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-20 17:51:15,390 [Worker-54: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-20 17:51:15,391 [Worker-54: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-20 17:51:15,396 [Worker-54: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-20 17:51:15,396 [Worker-54: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-20 17:51:15,396 [Worker-54: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-service\src\test\resources
2025-06-20 17:51:39,550 [Worker-50: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-20 17:51:39,550 [Worker-50: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-20 17:51:39,551 [Worker-50: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-20 17:51:39,551 [Worker-50: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-20 17:51:39,556 [Worker-50: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-20 17:51:39,557 [Worker-50: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-20 17:51:39,557 [Worker-50: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-service\src\test\resources
2025-06-20 17:51:43,410 [Worker-54: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-20 17:51:43,410 [Worker-54: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-20 17:51:43,411 [Worker-54: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-20 17:51:43,411 [Worker-54: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-20 17:51:43,415 [Worker-54: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-20 17:51:43,416 [Worker-54: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-20 17:51:43,416 [Worker-54: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-service\src\test\resources
2025-06-20 18:02:03,026 [Worker-69: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-20 18:02:03,026 [Worker-69: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-20 18:02:03,027 [Worker-69: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-20 18:02:03,027 [Worker-69: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-20 18:02:03,031 [Worker-69: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-20 18:02:03,031 [Worker-69: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-20 18:02:03,031 [Worker-69: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-service\src\test\resources
2025-06-20 18:02:36,053 [Worker-50: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-20 18:02:36,053 [Worker-50: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-20 18:02:36,053 [Worker-50: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-20 18:02:36,054 [Worker-50: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-20 18:02:36,059 [Worker-50: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-20 18:02:36,059 [Worker-50: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-20 18:02:36,059 [Worker-50: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-service\src\test\resources
2025-06-20 18:03:34,242 [Worker-50: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-20 18:03:34,243 [Worker-50: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-20 18:03:34,244 [Worker-50: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-20 18:03:34,244 [Worker-50: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-20 18:03:34,247 [Worker-50: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-20 18:03:34,247 [Worker-50: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-20 18:03:34,247 [Worker-50: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-service\src\test\resources
2025-06-20 18:03:57,233 [Worker-68: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-20 18:03:57,233 [Worker-68: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-20 18:03:57,234 [Worker-68: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-20 18:03:57,234 [Worker-68: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-20 18:03:57,237 [Worker-68: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-20 18:03:57,237 [Worker-68: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-20 18:03:57,237 [Worker-68: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-service\src\test\resources
2025-06-20 18:04:03,210 [Worker-71: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-20 18:04:03,210 [Worker-71: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-20 18:04:03,211 [Worker-71: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-20 18:04:03,211 [Worker-71: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-20 18:04:03,214 [Worker-71: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-20 18:04:03,215 [Worker-71: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-20 18:04:03,215 [Worker-71: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-service\src\test\resources
2025-06-20 18:09:47,007 [Worker-67: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-20 18:09:47,007 [Worker-67: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-20 18:09:47,007 [Worker-67: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-20 18:09:47,007 [Worker-67: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-20 18:09:47,010 [Worker-67: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-20 18:09:47,010 [Worker-67: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-20 18:09:47,010 [Worker-67: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-service\src\test\resources
2025-06-20 18:10:33,442 [Worker-64: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-20 18:10:33,442 [Worker-64: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-20 18:10:33,442 [Worker-64: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-20 18:10:33,443 [Worker-64: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-20 18:10:33,446 [Worker-64: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-20 18:10:33,446 [Worker-64: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-20 18:10:33,447 [Worker-64: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-service\src\test\resources
2025-06-20 18:13:11,957 [Worker-70: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-20 18:13:11,957 [Worker-70: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-20 18:13:11,957 [Worker-70: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-20 18:13:11,958 [Worker-70: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-20 18:13:11,960 [Worker-70: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-20 18:13:11,960 [Worker-70: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-20 18:13:11,960 [Worker-70: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-service\src\test\resources
2025-06-20 20:16:00,636 [Worker-2: Loading available Gradle versions] INFO  o.e.b.c.i.u.g.PublishedGradleVersions - Gradle version information cache is up-to-date. Trying to read.
2025-06-20 20:26:13,357 [Worker-38: Building] INFO  o.e.m.c.i.p.WorkspaceClassifierResolverManager - Resolving P/dev-c-yy-gz-aipoerp-api: com.yonyou.ucf:c-yy-gz-aipoerp-api:ddm-3.0-RELEASE with classifier  to /dev-c-yy-gz-aipoerp-api/target/classes
2025-06-20 20:26:13,360 [Worker-38: Building] INFO  o.e.m.c.i.p.WorkspaceClassifierResolverManager - Resolving P/dev-c-yy-gz-aipoerp-infrastructure: com.yonyou.ucf:c-yy-gz-aipoerp-infrastructure:ddm-3.0-RELEASE with classifier  to /dev-c-yy-gz-aipoerp-infrastructure/target/classes
2025-06-20 20:26:13,627 [Worker-38: Building] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-06-20 20:26:13,636 [Worker-38: Building] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-06-20 20:26:14,004 [Worker-38: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-20 20:26:14,004 [Worker-38: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-20 20:26:14,007 [Worker-38: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-20 20:26:14,008 [Worker-38: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-20 20:26:14,012 [Worker-38: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-20 20:26:14,013 [Worker-38: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-20 20:26:14,013 [Worker-38: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-service\src\test\resources
2025-06-20 20:26:14,016 [Worker-38: Building] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-06-20 20:26:14,017 [Worker-38: Building] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-06-20 20:26:17,079 [Worker-10: Building] INFO  o.e.m.c.i.p.WorkspaceClassifierResolverManager - Resolving P/dev-c-yy-gz-aipoerp-api: com.yonyou.ucf:c-yy-gz-aipoerp-api:ddm-3.0-RELEASE with classifier  to /dev-c-yy-gz-aipoerp-api/target/classes
2025-06-20 20:26:17,080 [Worker-10: Building] INFO  o.e.m.c.i.p.WorkspaceClassifierResolverManager - Resolving P/dev-c-yy-gz-aipoerp-app: com.yonyou.ucf:c-yy-gz-aipoerp-app:ddm-3.0-RELEASE with classifier  to /dev-c-yy-gz-aipoerp-app/target/classes
2025-06-20 20:26:17,080 [Worker-10: Building] INFO  o.e.m.c.i.p.WorkspaceClassifierResolverManager - Resolving P/dev-c-yy-gz-aipoerp-infrastructure: com.yonyou.ucf:c-yy-gz-aipoerp-infrastructure:ddm-3.0-RELEASE with classifier  to /dev-c-yy-gz-aipoerp-infrastructure/target/classes
2025-06-20 20:26:17,164 [Worker-10: Building] INFO  o.e.m.c.i.p.WorkspaceClassifierResolverManager - Resolving P/dev-c-yy-gz-aipoerp-service: com.yonyou.ucf:c-yy-gz-aipoerp-service:ddm-3.0-RELEASE with classifier  to /dev-c-yy-gz-aipoerp-service/target/classes
2025-06-20 20:26:17,272 [Worker-10: Building] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-06-20 20:26:17,273 [Worker-10: Building] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-06-20 20:27:53,074 [Worker-23: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-20 20:27:53,074 [Worker-23: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-20 20:27:53,076 [Worker-23: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-20 20:27:53,076 [Worker-23: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-20 20:27:53,080 [Worker-23: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-20 20:27:53,080 [Worker-23: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-20 20:27:53,080 [Worker-23: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-service\src\test\resources
2025-06-20 20:28:45,748 [Worker-20: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-20 20:28:45,748 [Worker-20: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-20 20:28:45,748 [Worker-20: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-20 20:28:45,749 [Worker-20: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-20 20:28:45,753 [Worker-20: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-20 20:28:45,753 [Worker-20: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-20 20:28:45,753 [Worker-20: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-service\src\test\resources
2025-06-20 20:31:02,274 [Worker-10: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-20 20:31:02,274 [Worker-10: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-20 20:31:02,274 [Worker-10: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-20 20:31:02,274 [Worker-10: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-20 20:31:02,278 [Worker-10: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-20 20:31:02,278 [Worker-10: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-20 20:31:02,278 [Worker-10: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-service\src\test\resources
2025-06-20 20:31:33,321 [Worker-23: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-20 20:31:33,322 [Worker-23: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-20 20:31:33,322 [Worker-23: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-20 20:31:33,322 [Worker-23: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-20 20:31:33,326 [Worker-23: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-20 20:31:33,326 [Worker-23: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-20 20:31:33,326 [Worker-23: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-service\src\test\resources
2025-06-20 20:36:53,437 [Worker-40: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-20 20:36:53,437 [Worker-40: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-20 20:36:53,437 [Worker-40: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-20 20:36:53,437 [Worker-40: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-20 20:36:53,441 [Worker-40: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-20 20:36:53,441 [Worker-40: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-20 20:36:53,441 [Worker-40: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-service\src\test\resources
2025-06-20 20:41:05,570 [Worker-10: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-20 20:41:05,570 [Worker-10: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-20 20:41:05,570 [Worker-10: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-20 20:41:05,570 [Worker-10: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-20 20:41:05,574 [Worker-10: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-20 20:41:05,574 [Worker-10: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-20 20:41:05,574 [Worker-10: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-service\src\test\resources
2025-06-20 20:41:21,960 [Worker-40: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-20 20:41:21,960 [Worker-40: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-20 20:41:21,960 [Worker-40: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-20 20:41:21,960 [Worker-40: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-20 20:41:21,964 [Worker-40: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-20 20:41:21,964 [Worker-40: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-20 20:41:21,964 [Worker-40: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-service\src\test\resources
2025-06-20 20:43:55,919 [Worker-10: Cleaning all projects] INFO  o.e.m.c.i.p.WorkspaceClassifierResolverManager - Resolving P/dev-c-yy-gz-aipoerp-infrastructure: com.yonyou.ucf:c-yy-gz-aipoerp-infrastructure:ddm-3.0-RELEASE with classifier  to /dev-c-yy-gz-aipoerp-infrastructure/target/classes
2025-06-20 20:43:56,047 [Worker-10: Cleaning all projects] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-06-20 20:43:56,049 [Worker-10: Cleaning all projects] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-06-20 20:43:57,330 [Worker-10: Cleaning all projects] INFO  o.e.m.c.i.p.WorkspaceClassifierResolverManager - Resolving P/dev-c-yy-gz-aipoerp-infrastructure: com.yonyou.ucf:c-yy-gz-aipoerp-infrastructure:ddm-3.0-RELEASE with classifier  to /dev-c-yy-gz-aipoerp-infrastructure/target/classes
2025-06-20 20:43:57,411 [Worker-10: Cleaning all projects] INFO  o.e.m.c.i.p.WorkspaceClassifierResolverManager - Resolving P/dev-c-yy-gz-aipoerp-service: com.yonyou.ucf:c-yy-gz-aipoerp-service:ddm-3.0-RELEASE with classifier  to /dev-c-yy-gz-aipoerp-service/target/classes
2025-06-20 20:43:57,458 [Worker-10: Cleaning all projects] INFO  o.e.m.c.i.p.WorkspaceClassifierResolverManager - Resolving P/dev-c-yy-gz-aipoerp-api: com.yonyou.ucf:c-yy-gz-aipoerp-api:ddm-3.0-RELEASE with classifier  to /dev-c-yy-gz-aipoerp-api/target/classes
2025-06-20 20:43:57,483 [Worker-10: Cleaning all projects] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-06-20 20:43:57,484 [Worker-10: Cleaning all projects] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-06-20 20:43:57,544 [Worker-10: Cleaning all projects] INFO  o.e.m.w.i.f.ResourceFilteringBuildParticipant - Cleaning filtered folder for dev-c-yy-gz-aipoerp-bootstrap
2025-06-20 20:43:58,702 [Worker-10: Cleaning all projects] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-06-20 20:43:58,704 [Worker-10: Cleaning all projects] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-06-20 20:43:59,087 [Worker-41: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-20 20:43:59,088 [Worker-41: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-20 20:43:59,088 [Worker-41: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-infrastructure\src\main\resources
2025-06-20 20:43:59,088 [Worker-41: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-infrastructure\src\main\resources
2025-06-20 20:43:59,091 [Worker-41: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-20 20:43:59,091 [Worker-41: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-20 20:43:59,091 [Worker-41: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-infrastructure\src\test\resources
2025-06-20 20:43:59,092 [Worker-41: Building] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-06-20 20:43:59,094 [Worker-41: Building] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-06-20 20:43:59,194 [Worker-41: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-20 20:43:59,194 [Worker-41: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-20 20:43:59,194 [Worker-41: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-api\src\main\resources
2025-06-20 20:43:59,194 [Worker-41: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-api\src\main\resources
2025-06-20 20:43:59,198 [Worker-41: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-20 20:43:59,198 [Worker-41: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-20 20:43:59,198 [Worker-41: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-api\src\test\resources
2025-06-20 20:43:59,200 [Worker-41: Building] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-06-20 20:43:59,201 [Worker-41: Building] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-06-20 20:44:01,110 [Worker-41: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-20 20:44:01,110 [Worker-41: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-20 20:44:01,110 [Worker-41: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-20 20:44:01,110 [Worker-41: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-20 20:44:01,113 [Worker-41: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-20 20:44:01,113 [Worker-41: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-20 20:44:01,113 [Worker-41: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-service\src\test\resources
2025-06-20 20:44:01,114 [Worker-41: Building] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-06-20 20:44:01,115 [Worker-41: Building] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-06-20 20:44:01,376 [Worker-41: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-20 20:44:01,376 [Worker-41: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-20 20:44:01,376 [Worker-41: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-app\src\main\resources
2025-06-20 20:44:01,376 [Worker-41: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-app\src\main\resources
2025-06-20 20:44:01,379 [Worker-41: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-20 20:44:01,380 [Worker-41: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-20 20:44:01,380 [Worker-41: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-app\src\test\resources
2025-06-20 20:44:01,381 [Worker-41: Building] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-06-20 20:44:01,382 [Worker-41: Building] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-06-20 20:44:01,515 [Worker-41: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-20 20:44:01,516 [Worker-41: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-20 20:44:01,516 [Worker-41: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 13 resources
2025-06-20 20:44:01,588 [Worker-41: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 2 resources
2025-06-20 20:44:01,596 [Worker-41: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-20 20:44:01,596 [Worker-41: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-20 20:44:01,596 [Worker-41: Building] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-bootstrap\src\test\resources
2025-06-20 20:44:01,597 [Worker-41: Building] INFO  o.e.m.w.i.f.ResourceFilteringBuildParticipant - Changed resources require a complete clean of filtered resources of dev-c-yy-gz-aipoerp-bootstrap
2025-06-20 20:44:01,599 [Worker-41: Building] INFO  o.e.m.w.i.f.ResourceFilteringBuildParticipant - Executing resource filtering for dev-c-yy-gz-aipoerp-bootstrap
2025-06-20 20:44:01,601 [Worker-41: Building] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-06-20 20:44:01,602 [Worker-41: Building] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-06-20 20:50:35,153 [Worker-41: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-20 20:50:35,153 [Worker-41: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-20 20:50:35,154 [Worker-41: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-20 20:50:35,154 [Worker-41: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-20 20:50:35,157 [Worker-41: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-20 20:50:35,157 [Worker-41: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-20 20:50:35,157 [Worker-41: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-service\src\test\resources
2025-06-20 21:01:30,811 [Worker-46: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-20 21:01:30,811 [Worker-46: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-20 21:01:30,812 [Worker-46: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-20 21:01:30,812 [Worker-46: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-20 21:01:30,816 [Worker-46: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-20 21:01:30,816 [Worker-46: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-20 21:01:30,816 [Worker-46: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-service\src\test\resources
2025-06-20 21:06:38,802 [Worker-46: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-20 21:06:38,803 [Worker-46: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-20 21:06:38,803 [Worker-46: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-20 21:06:38,803 [Worker-46: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-20 21:06:38,806 [Worker-46: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-20 21:06:38,806 [Worker-46: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-20 21:06:38,806 [Worker-46: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-service\src\test\resources
2025-06-20 21:08:21,969 [Worker-47: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-20 21:08:21,969 [Worker-47: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-20 21:08:21,970 [Worker-47: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-20 21:08:21,970 [Worker-47: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-20 21:08:21,973 [Worker-47: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-20 21:08:21,973 [Worker-47: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-20 21:08:21,973 [Worker-47: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-service\src\test\resources
2025-06-20 21:09:33,624 [Worker-10: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-20 21:09:33,624 [Worker-10: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-20 21:09:33,624 [Worker-10: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-20 21:09:33,624 [Worker-10: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-20 21:09:33,626 [Worker-10: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-20 21:09:33,627 [Worker-10: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-20 21:09:33,627 [Worker-10: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-service\src\test\resources
2025-06-20 21:10:30,526 [Worker-10: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-20 21:10:30,527 [Worker-10: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-20 21:10:30,527 [Worker-10: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-20 21:10:30,527 [Worker-10: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-20 21:10:30,530 [Worker-10: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-20 21:10:30,530 [Worker-10: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-20 21:10:30,530 [Worker-10: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-service\src\test\resources
2025-06-20 21:15:02,123 [Worker-51: Fetching remote <NAME_EMAIL>:005/005/aipogroup/aipor6/aipoerp.git] INFO  o.a.s.c.i.DefaultIoServiceFactoryFactory - No detected/configured IoServiceFactoryFactory; using Nio2ServiceFactoryFactory
2025-06-21 10:00:22,408 [Worker-10: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-21 10:00:22,408 [Worker-10: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-21 10:00:22,408 [Worker-10: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-21 10:00:22,408 [Worker-10: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-21 10:00:22,412 [Worker-10: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-21 10:00:22,412 [Worker-10: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-21 10:00:22,412 [Worker-10: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-service\src\test\resources
2025-06-21 10:00:48,301 [Worker-68: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-21 10:00:48,302 [Worker-68: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-21 10:00:48,302 [Worker-68: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-21 10:00:48,302 [Worker-68: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-21 10:00:48,305 [Worker-68: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-21 10:00:48,305 [Worker-68: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-21 10:00:48,305 [Worker-68: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-service\src\test\resources
2025-06-21 10:01:49,450 [Worker-10: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-21 10:01:49,450 [Worker-10: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-21 10:01:49,450 [Worker-10: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-21 10:01:49,450 [Worker-10: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-21 10:01:49,453 [Worker-10: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-21 10:01:49,453 [Worker-10: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-21 10:01:49,453 [Worker-10: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-service\src\test\resources
2025-06-21 11:22:34,119 [Worker-66: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-21 11:22:34,120 [Worker-66: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-21 11:22:34,120 [Worker-66: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-21 11:22:34,121 [Worker-66: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-21 11:22:34,125 [Worker-66: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-21 11:22:34,125 [Worker-66: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-21 11:22:34,125 [Worker-66: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-service\src\test\resources
2025-06-21 14:11:19,989 [Worker-77: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-21 14:11:19,990 [Worker-77: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-21 14:11:19,990 [Worker-77: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-21 14:11:19,991 [Worker-77: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-21 14:11:19,996 [Worker-77: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-21 14:11:19,996 [Worker-77: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-21 14:11:19,996 [Worker-77: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-service\src\test\resources
2025-06-21 14:11:55,225 [Worker-90: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-21 14:11:55,226 [Worker-90: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-21 14:11:55,226 [Worker-90: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-21 14:11:55,226 [Worker-90: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-21 14:11:55,231 [Worker-90: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-21 14:11:55,231 [Worker-90: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-21 14:11:55,231 [Worker-90: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-service\src\test\resources
2025-06-23 09:41:14,880 [Worker-6: Loading available Gradle versions] INFO  o.e.b.c.i.u.g.PublishedGradleVersions - Gradle version information cache is out-of-date. Trying to update.
2025-06-23 09:41:15,524 [Worker-6: Loading available Gradle versions] INFO  o.e.b.c.i.u.g.PublishedGradleVersions - Updating Gradle version information cache failed. Using outdated cache.
2025-06-24 10:34:48,695 [Worker-7: Loading available Gradle versions] INFO  o.e.b.c.i.u.g.PublishedGradleVersions - Gradle version information cache is out-of-date. Trying to update.
2025-06-25 08:32:20,592 [Worker-4: Loading available Gradle versions] INFO  o.e.b.c.i.u.g.PublishedGradleVersions - Gradle version information cache is up-to-date. Trying to read.
2025-06-25 15:43:42,808 [Worker-53: Building Workspace] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-06-25 15:43:42,821 [Worker-53: Building Workspace] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-06-25 15:43:43,084 [Worker-53: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-25 15:43:43,084 [Worker-53: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-25 15:43:43,084 [Worker-53: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-infrastructure\src\main\resources
2025-06-25 15:43:43,084 [Worker-53: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-infrastructure\src\main\resources
2025-06-25 15:43:43,088 [Worker-53: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-25 15:43:43,088 [Worker-53: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-25 15:43:43,088 [Worker-53: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-infrastructure\src\test\resources
2025-06-25 15:43:43,090 [Worker-53: Building Workspace] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-06-25 15:43:43,092 [Worker-53: Building Workspace] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-06-25 15:43:44,953 [Worker-53: Building Workspace] INFO  o.e.m.c.i.p.WorkspaceClassifierResolverManager - Resolving P/dev-c-yy-gz-aipoerp-infrastructure: com.yonyou.ucf:c-yy-gz-aipoerp-infrastructure:ddm-3.0-RELEASE with classifier  to /dev-c-yy-gz-aipoerp-infrastructure/target/classes
2025-06-25 15:43:45,074 [Worker-53: Building Workspace] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-06-25 15:43:45,075 [Worker-53: Building Workspace] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-06-25 15:43:45,081 [Worker-53: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-25 15:43:45,081 [Worker-53: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-25 15:43:45,081 [Worker-53: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-api\src\main\resources
2025-06-25 15:43:45,081 [Worker-53: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-api\src\main\resources
2025-06-25 15:43:45,084 [Worker-53: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-25 15:43:45,084 [Worker-53: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-25 15:43:45,084 [Worker-53: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-api\src\test\resources
2025-06-25 15:43:45,086 [Worker-53: Building Workspace] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-06-25 15:43:45,086 [Worker-53: Building Workspace] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-06-25 15:43:48,522 [Worker-53: Building Workspace] INFO  o.e.m.c.i.p.WorkspaceClassifierResolverManager - Resolving P/dev-c-yy-gz-aipoerp-api: com.yonyou.ucf:c-yy-gz-aipoerp-api:ddm-3.0-RELEASE with classifier  to /dev-c-yy-gz-aipoerp-api/target/classes
2025-06-25 15:43:48,523 [Worker-53: Building Workspace] INFO  o.e.m.c.i.p.WorkspaceClassifierResolverManager - Resolving P/dev-c-yy-gz-aipoerp-infrastructure: com.yonyou.ucf:c-yy-gz-aipoerp-infrastructure:ddm-3.0-RELEASE with classifier  to /dev-c-yy-gz-aipoerp-infrastructure/target/classes
2025-06-25 15:43:48,578 [Worker-53: Building Workspace] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-06-25 15:43:48,580 [Worker-53: Building Workspace] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-06-25 15:43:48,587 [Worker-53: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-25 15:43:48,587 [Worker-53: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-25 15:43:48,589 [Worker-53: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-25 15:43:48,590 [Worker-53: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-25 15:43:48,593 [Worker-53: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-25 15:43:48,593 [Worker-53: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-25 15:43:48,593 [Worker-53: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-service\src\test\resources
2025-06-25 15:43:48,594 [Worker-53: Building Workspace] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-06-25 15:43:48,595 [Worker-53: Building Workspace] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-06-25 15:43:50,095 [Worker-53: Building Workspace] INFO  o.e.m.c.i.p.WorkspaceClassifierResolverManager - Resolving P/dev-c-yy-gz-aipoerp-infrastructure: com.yonyou.ucf:c-yy-gz-aipoerp-infrastructure:ddm-3.0-RELEASE with classifier  to /dev-c-yy-gz-aipoerp-infrastructure/target/classes
2025-06-25 15:43:50,177 [Worker-53: Building Workspace] INFO  o.e.m.c.i.p.WorkspaceClassifierResolverManager - Resolving P/dev-c-yy-gz-aipoerp-service: com.yonyou.ucf:c-yy-gz-aipoerp-service:ddm-3.0-RELEASE with classifier  to /dev-c-yy-gz-aipoerp-service/target/classes
2025-06-25 15:43:50,223 [Worker-53: Building Workspace] INFO  o.e.m.c.i.p.WorkspaceClassifierResolverManager - Resolving P/dev-c-yy-gz-aipoerp-api: com.yonyou.ucf:c-yy-gz-aipoerp-api:ddm-3.0-RELEASE with classifier  to /dev-c-yy-gz-aipoerp-api/target/classes
2025-06-25 15:43:50,245 [Worker-53: Building Workspace] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-06-25 15:43:50,247 [Worker-53: Building Workspace] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-06-25 15:43:50,250 [Worker-53: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-25 15:43:50,250 [Worker-53: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-25 15:43:50,250 [Worker-53: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-app\src\main\resources
2025-06-25 15:43:50,250 [Worker-53: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-app\src\main\resources
2025-06-25 15:43:50,254 [Worker-53: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-25 15:43:50,254 [Worker-53: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-25 15:43:50,254 [Worker-53: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-app\src\test\resources
2025-06-25 15:43:50,255 [Worker-53: Building Workspace] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-06-25 15:43:50,255 [Worker-53: Building Workspace] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-06-25 15:43:51,547 [Worker-53: Building Workspace] INFO  o.e.m.c.i.p.WorkspaceClassifierResolverManager - Resolving P/dev-c-yy-gz-aipoerp-api: com.yonyou.ucf:c-yy-gz-aipoerp-api:ddm-3.0-RELEASE with classifier  to /dev-c-yy-gz-aipoerp-api/target/classes
2025-06-25 15:43:51,548 [Worker-53: Building Workspace] INFO  o.e.m.c.i.p.WorkspaceClassifierResolverManager - Resolving P/dev-c-yy-gz-aipoerp-app: com.yonyou.ucf:c-yy-gz-aipoerp-app:ddm-3.0-RELEASE with classifier  to /dev-c-yy-gz-aipoerp-app/target/classes
2025-06-25 15:43:51,549 [Worker-53: Building Workspace] INFO  o.e.m.c.i.p.WorkspaceClassifierResolverManager - Resolving P/dev-c-yy-gz-aipoerp-infrastructure: com.yonyou.ucf:c-yy-gz-aipoerp-infrastructure:ddm-3.0-RELEASE with classifier  to /dev-c-yy-gz-aipoerp-infrastructure/target/classes
2025-06-25 15:43:51,625 [Worker-53: Building Workspace] INFO  o.e.m.c.i.p.WorkspaceClassifierResolverManager - Resolving P/dev-c-yy-gz-aipoerp-service: com.yonyou.ucf:c-yy-gz-aipoerp-service:ddm-3.0-RELEASE with classifier  to /dev-c-yy-gz-aipoerp-service/target/classes
2025-06-25 15:43:51,717 [Worker-53: Building Workspace] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-06-25 15:43:51,719 [Worker-53: Building Workspace] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-06-25 15:43:51,734 [Worker-53: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-25 15:43:51,734 [Worker-53: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-25 15:43:51,735 [Worker-53: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 13 resources
2025-06-25 15:43:51,778 [Worker-53: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 2 resources
2025-06-25 15:43:51,786 [Worker-53: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-25 15:43:51,786 [Worker-53: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-25 15:43:51,786 [Worker-53: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-bootstrap\src\test\resources
2025-06-25 15:43:51,819 [Worker-53: Building Workspace] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-06-25 15:43:51,820 [Worker-53: Building Workspace] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-06-25 15:44:09,726 [Worker-55: Launching FileNameTest] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-25 15:44:09,727 [Worker-55: Launching FileNameTest] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-25 15:44:09,727 [Worker-55: Launching FileNameTest] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-25 15:44:09,727 [Worker-55: Launching FileNameTest] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-25 15:44:09,730 [Worker-55: Launching FileNameTest] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-25 15:44:09,730 [Worker-55: Launching FileNameTest] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-25 15:44:09,730 [Worker-55: Launching FileNameTest] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-service\src\test\resources
2025-06-25 15:44:09,733 [Worker-55: Launching FileNameTest] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-06-25 15:44:09,734 [Worker-55: Launching FileNameTest] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-06-25 15:44:12,107 [Worker-55: Launching FileNameTest] INFO  o.e.m.c.i.p.WorkspaceClassifierResolverManager - Resolving P/dev-c-yy-gz-aipoerp-api: com.yonyou.ucf:c-yy-gz-aipoerp-api:ddm-3.0-RELEASE with classifier null to /dev-c-yy-gz-aipoerp-api/target/classes
2025-06-25 15:44:12,108 [Worker-55: Launching FileNameTest] INFO  o.e.m.c.i.p.WorkspaceClassifierResolverManager - Resolving P/dev-c-yy-gz-aipoerp-infrastructure: com.yonyou.ucf:c-yy-gz-aipoerp-infrastructure:ddm-3.0-RELEASE with classifier null to /dev-c-yy-gz-aipoerp-infrastructure/target/classes
2025-06-25 15:47:23,647 [Worker-57: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-25 15:47:23,647 [Worker-57: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-25 15:47:23,648 [Worker-57: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-25 15:47:23,649 [Worker-57: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-25 15:47:23,651 [Worker-57: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-25 15:47:23,651 [Worker-57: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-25 15:47:23,651 [Worker-57: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-service\src\test\resources
2025-06-25 15:48:47,411 [Worker-47: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-25 15:48:47,411 [Worker-47: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-25 15:48:47,411 [Worker-47: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-25 15:48:47,412 [Worker-47: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-25 15:48:47,414 [Worker-47: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-25 15:48:47,414 [Worker-47: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-25 15:48:47,414 [Worker-47: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-service\src\test\resources
2025-06-25 15:50:11,220 [Worker-55: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-25 15:50:11,220 [Worker-55: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-25 15:50:11,220 [Worker-55: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-25 15:50:11,221 [Worker-55: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-25 15:50:11,224 [Worker-55: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-25 15:50:11,224 [Worker-55: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-25 15:50:11,225 [Worker-55: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-service\src\test\resources
2025-06-25 15:50:34,602 [Worker-56: Push to aipoerp refs/heads/prd - origin] INFO  o.a.s.c.i.DefaultIoServiceFactoryFactory - No detected/configured IoServiceFactoryFactory; using Nio2ServiceFactoryFactory
2025-06-26 08:46:59,862 [Worker-4: Loading available Gradle versions] INFO  o.e.b.c.i.u.g.PublishedGradleVersions - Gradle version information cache is out-of-date. Trying to update.
2025-06-26 11:46:24,582 [Worker-46: Download sources and javadoc] INFO  o.e.m.c.i.p.WorkspaceClassifierResolverManager - Resolving P/dev-c-yy-gz-aipoerp-api: com.yonyou.ucf:c-yy-gz-aipoerp-api:ddm-3.0-RELEASE with classifier  to /dev-c-yy-gz-aipoerp-api/target/classes
2025-06-26 11:46:24,585 [Worker-46: Download sources and javadoc] INFO  o.e.m.c.i.p.WorkspaceClassifierResolverManager - Resolving P/dev-c-yy-gz-aipoerp-infrastructure: com.yonyou.ucf:c-yy-gz-aipoerp-infrastructure:ddm-3.0-RELEASE with classifier  to /dev-c-yy-gz-aipoerp-infrastructure/target/classes
2025-06-26 11:46:34,477 [Worker-41: Building Workspace] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-06-26 11:46:34,486 [Worker-41: Building Workspace] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-06-26 11:46:34,866 [Worker-41: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-26 11:46:34,866 [Worker-41: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-26 11:46:34,877 [Worker-41: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-26 11:46:34,879 [Worker-41: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-26 11:46:34,884 [Worker-41: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-26 11:46:34,884 [Worker-41: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-26 11:46:34,884 [Worker-41: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-service\src\test\resources
2025-06-26 11:46:34,889 [Worker-41: Building Workspace] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-06-26 11:46:34,890 [Worker-41: Building Workspace] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-06-26 14:04:13,757 [Worker-65: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-26 14:04:13,757 [Worker-65: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-26 14:04:13,758 [Worker-65: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-26 14:04:13,758 [Worker-65: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-26 14:04:13,762 [Worker-65: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-26 14:04:13,762 [Worker-65: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-26 14:04:13,762 [Worker-65: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-service\src\test\resources
2025-06-26 14:04:15,421 [Worker-65: Building Workspace] INFO  o.e.m.c.i.p.WorkspaceClassifierResolverManager - Resolving P/dev-c-yy-gz-aipoerp-api: com.yonyou.ucf:c-yy-gz-aipoerp-api:ddm-3.0-RELEASE with classifier  to /dev-c-yy-gz-aipoerp-api/target/classes
2025-06-26 14:04:15,422 [Worker-65: Building Workspace] INFO  o.e.m.c.i.p.WorkspaceClassifierResolverManager - Resolving P/dev-c-yy-gz-aipoerp-app: com.yonyou.ucf:c-yy-gz-aipoerp-app:ddm-3.0-RELEASE with classifier  to /dev-c-yy-gz-aipoerp-app/target/classes
2025-06-26 14:04:15,422 [Worker-65: Building Workspace] INFO  o.e.m.c.i.p.WorkspaceClassifierResolverManager - Resolving P/dev-c-yy-gz-aipoerp-infrastructure: com.yonyou.ucf:c-yy-gz-aipoerp-infrastructure:ddm-3.0-RELEASE with classifier  to /dev-c-yy-gz-aipoerp-infrastructure/target/classes
2025-06-26 14:04:15,506 [Worker-65: Building Workspace] INFO  o.e.m.c.i.p.WorkspaceClassifierResolverManager - Resolving P/dev-c-yy-gz-aipoerp-service: com.yonyou.ucf:c-yy-gz-aipoerp-service:ddm-3.0-RELEASE with classifier  to /dev-c-yy-gz-aipoerp-service/target/classes
2025-06-26 14:04:15,606 [Worker-65: Building Workspace] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-06-26 14:04:15,607 [Worker-65: Building Workspace] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-06-26 14:05:41,936 [Worker-60: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-26 14:05:41,936 [Worker-60: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-26 14:05:41,936 [Worker-60: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-26 14:05:41,936 [Worker-60: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-26 14:05:41,939 [Worker-60: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-26 14:05:41,940 [Worker-60: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-26 14:05:41,940 [Worker-60: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-service\src\test\resources
2025-06-26 14:05:48,152 [Worker-65: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-26 14:05:48,153 [Worker-65: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-26 14:05:48,153 [Worker-65: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-26 14:05:48,153 [Worker-65: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-26 14:05:48,157 [Worker-65: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-26 14:05:48,157 [Worker-65: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-26 14:05:48,157 [Worker-65: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-service\src\test\resources
2025-06-26 14:06:09,269 [Worker-66: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-26 14:06:09,270 [Worker-66: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-26 14:06:09,270 [Worker-66: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-26 14:06:09,270 [Worker-66: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-26 14:06:09,274 [Worker-66: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-26 14:06:09,274 [Worker-66: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-26 14:06:09,274 [Worker-66: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-service\src\test\resources
2025-06-26 14:09:36,544 [Worker-65: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-26 14:09:36,544 [Worker-65: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-26 14:09:36,545 [Worker-65: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-26 14:09:36,545 [Worker-65: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-26 14:09:36,548 [Worker-65: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-26 14:09:36,548 [Worker-65: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-26 14:09:36,549 [Worker-65: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-service\src\test\resources
2025-06-26 14:40:53,983 [Worker-66: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-26 14:40:53,983 [Worker-66: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-26 14:40:53,984 [Worker-66: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-26 14:40:53,984 [Worker-66: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-26 14:40:53,989 [Worker-66: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-26 14:40:53,989 [Worker-66: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-26 14:40:53,989 [Worker-66: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-service\src\test\resources
2025-06-26 14:41:05,644 [Worker-73: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-26 14:41:05,644 [Worker-73: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-26 14:41:05,644 [Worker-73: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-26 14:41:05,645 [Worker-73: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-26 14:41:05,648 [Worker-73: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-26 14:41:05,648 [Worker-73: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-26 14:41:05,648 [Worker-73: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-service\src\test\resources
2025-06-26 14:41:17,628 [Worker-73: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-26 14:41:17,628 [Worker-73: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-26 14:41:17,629 [Worker-73: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-26 14:41:17,629 [Worker-73: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-26 14:41:17,633 [Worker-73: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-26 14:41:17,633 [Worker-73: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-26 14:41:17,633 [Worker-73: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-service\src\test\resources
2025-06-26 14:43:42,455 [Worker-66: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-26 14:43:42,455 [Worker-66: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-26 14:43:42,456 [Worker-66: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-26 14:43:42,456 [Worker-66: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-26 14:43:42,460 [Worker-66: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-26 14:43:42,460 [Worker-66: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-26 14:43:42,460 [Worker-66: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-service\src\test\resources
2025-06-26 14:43:51,976 [Worker-69: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-26 14:43:51,976 [Worker-69: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-26 14:43:51,977 [Worker-69: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-26 14:43:51,977 [Worker-69: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-26 14:43:51,980 [Worker-69: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-26 14:43:51,980 [Worker-69: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-26 14:43:51,980 [Worker-69: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-service\src\test\resources
2025-06-26 14:47:59,234 [Worker-69: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-26 14:47:59,234 [Worker-69: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-26 14:47:59,234 [Worker-69: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-26 14:47:59,235 [Worker-69: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-26 14:47:59,237 [Worker-69: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-26 14:47:59,237 [Worker-69: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-26 14:47:59,237 [Worker-69: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-service\src\test\resources
2025-06-26 14:49:10,053 [Worker-67: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-26 14:49:10,053 [Worker-67: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-26 14:49:10,054 [Worker-67: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-26 14:49:10,054 [Worker-67: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-26 14:49:10,057 [Worker-67: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-26 14:49:10,057 [Worker-67: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-26 14:49:10,057 [Worker-67: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-service\src\test\resources
2025-06-26 14:52:58,245 [Worker-66: Building Workspace] INFO  o.e.m.c.i.p.WorkspaceClassifierResolverManager - Resolving P/dev-c-yy-gz-aipoerp-infrastructure: com.yonyou.ucf:c-yy-gz-aipoerp-infrastructure:ddm-3.0-RELEASE with classifier  to /dev-c-yy-gz-aipoerp-infrastructure/target/classes
2025-06-26 14:52:58,326 [Worker-66: Building Workspace] INFO  o.e.m.c.i.p.WorkspaceClassifierResolverManager - Resolving P/dev-c-yy-gz-aipoerp-service: com.yonyou.ucf:c-yy-gz-aipoerp-service:ddm-3.0-RELEASE with classifier  to /dev-c-yy-gz-aipoerp-service/target/classes
2025-06-26 14:52:58,378 [Worker-66: Building Workspace] INFO  o.e.m.c.i.p.WorkspaceClassifierResolverManager - Resolving P/dev-c-yy-gz-aipoerp-api: com.yonyou.ucf:c-yy-gz-aipoerp-api:ddm-3.0-RELEASE with classifier  to /dev-c-yy-gz-aipoerp-api/target/classes
2025-06-26 14:52:58,406 [Worker-66: Building Workspace] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-06-26 14:52:58,408 [Worker-66: Building Workspace] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-06-26 14:52:58,414 [Worker-66: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-26 14:52:58,414 [Worker-66: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-26 14:52:58,414 [Worker-66: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-app\src\main\resources
2025-06-26 14:52:58,414 [Worker-66: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-app\src\main\resources
2025-06-26 14:52:58,418 [Worker-66: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-26 14:52:58,418 [Worker-66: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-26 14:52:58,418 [Worker-66: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-app\src\test\resources
2025-06-26 14:52:58,419 [Worker-66: Building Workspace] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-06-26 14:52:58,420 [Worker-66: Building Workspace] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-06-26 14:55:30,606 [Worker-74: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-26 14:55:30,606 [Worker-74: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-26 14:55:30,606 [Worker-74: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-app\src\main\resources
2025-06-26 14:55:30,606 [Worker-74: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-app\src\main\resources
2025-06-26 14:55:30,610 [Worker-74: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-26 14:55:30,610 [Worker-74: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-26 14:55:30,610 [Worker-74: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-app\src\test\resources
2025-06-26 14:57:06,140 [Worker-67: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-26 14:57:06,140 [Worker-67: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-26 14:57:06,140 [Worker-67: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-app\src\main\resources
2025-06-26 14:57:06,140 [Worker-67: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-app\src\main\resources
2025-06-26 14:57:06,144 [Worker-67: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-26 14:57:06,144 [Worker-67: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-26 14:57:06,144 [Worker-67: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-app\src\test\resources
2025-06-26 14:57:18,563 [Worker-67: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-26 14:57:18,563 [Worker-67: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-26 14:57:18,563 [Worker-67: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-app\src\main\resources
2025-06-26 14:57:18,563 [Worker-67: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-app\src\main\resources
2025-06-26 14:57:18,566 [Worker-67: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-26 14:57:18,566 [Worker-67: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-26 14:57:18,566 [Worker-67: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-app\src\test\resources
2025-06-26 15:16:04,830 [Worker-73: Push to aipoerp refs/heads/prd - origin] INFO  o.a.s.c.i.DefaultIoServiceFactoryFactory - No detected/configured IoServiceFactoryFactory; using Nio2ServiceFactoryFactory
2025-06-26 16:33:10,915 [Worker-89: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-26 16:33:10,915 [Worker-89: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-26 16:33:10,915 [Worker-89: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-26 16:33:10,916 [Worker-89: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-26 16:33:10,919 [Worker-89: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-26 16:33:10,919 [Worker-89: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-26 16:33:10,919 [Worker-89: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-service\src\test\resources
2025-06-26 16:33:11,067 [Worker-89: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-26 16:33:11,067 [Worker-89: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-26 16:33:11,067 [Worker-89: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-app\src\main\resources
2025-06-26 16:33:11,067 [Worker-89: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-app\src\main\resources
2025-06-26 16:33:11,069 [Worker-89: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-26 16:33:11,069 [Worker-89: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-26 16:33:11,069 [Worker-89: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-app\src\test\resources
2025-06-26 17:13:55,718 [Worker-76: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-26 17:13:55,718 [Worker-76: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-26 17:13:55,718 [Worker-76: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-26 17:13:55,719 [Worker-76: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-26 17:13:55,723 [Worker-76: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-26 17:13:55,723 [Worker-76: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-26 17:13:55,723 [Worker-76: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-service\src\test\resources
2025-06-26 17:13:55,893 [Worker-76: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-26 17:13:55,893 [Worker-76: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-26 17:13:55,893 [Worker-76: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-app\src\main\resources
2025-06-26 17:13:55,893 [Worker-76: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-app\src\main\resources
2025-06-26 17:13:55,896 [Worker-76: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-26 17:13:55,896 [Worker-76: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-26 17:13:55,896 [Worker-76: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-app\src\test\resources
2025-06-26 17:20:17,453 [Worker-82: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-26 17:20:17,454 [Worker-82: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-26 17:20:17,454 [Worker-82: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-26 17:20:17,454 [Worker-82: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-26 17:20:17,458 [Worker-82: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-26 17:20:17,458 [Worker-82: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-26 17:20:17,458 [Worker-82: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-service\src\test\resources
2025-06-26 17:31:10,520 [Worker-82: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-26 17:31:10,520 [Worker-82: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-26 17:31:10,521 [Worker-82: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-26 17:31:10,521 [Worker-82: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-26 17:31:10,523 [Worker-82: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-26 17:31:10,523 [Worker-82: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-26 17:31:10,523 [Worker-82: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-service\src\test\resources
2025-06-26 17:34:30,469 [Worker-82: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-26 17:34:30,470 [Worker-82: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-26 17:34:30,470 [Worker-82: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-26 17:34:30,470 [Worker-82: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-26 17:34:30,472 [Worker-82: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-26 17:34:30,472 [Worker-82: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-26 17:34:30,473 [Worker-82: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-service\src\test\resources
2025-06-26 19:17:56,476 [Worker-104: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-26 19:17:56,477 [Worker-104: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-26 19:17:56,478 [Worker-104: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-26 19:17:56,478 [Worker-104: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-26 19:17:56,484 [Worker-104: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-26 19:17:56,484 [Worker-104: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-26 19:17:56,484 [Worker-104: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-service\src\test\resources
2025-06-26 19:17:56,697 [Worker-104: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-26 19:17:56,697 [Worker-104: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-26 19:17:56,697 [Worker-104: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-app\src\main\resources
2025-06-26 19:17:56,697 [Worker-104: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-app\src\main\resources
2025-06-26 19:17:56,700 [Worker-104: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-26 19:17:56,700 [Worker-104: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-26 19:17:56,700 [Worker-104: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-app\src\test\resources
2025-06-26 19:18:16,985 [Worker-99: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-26 19:18:16,985 [Worker-99: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-26 19:18:16,985 [Worker-99: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-app\src\main\resources
2025-06-26 19:18:16,985 [Worker-99: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-app\src\main\resources
2025-06-26 19:18:16,989 [Worker-99: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-26 19:18:16,989 [Worker-99: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-26 19:18:16,989 [Worker-99: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-app\src\test\resources
2025-06-26 19:43:11,316 [Worker-121: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-26 19:43:11,317 [Worker-121: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-26 19:43:11,317 [Worker-121: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-app\src\main\resources
2025-06-26 19:43:11,317 [Worker-121: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-app\src\main\resources
2025-06-26 19:43:11,320 [Worker-121: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-26 19:43:11,320 [Worker-121: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-26 19:43:11,320 [Worker-121: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-app\src\test\resources
2025-06-27 08:51:03,004 [Worker-6: Loading available Gradle versions] INFO  o.e.b.c.i.u.g.PublishedGradleVersions - Gradle version information cache is out-of-date. Trying to update.
2025-06-27 10:05:26,973 [Worker-29: Building Workspace] INFO  o.e.m.c.i.e.AbstractTransferListenerAdapter - Downloading https://repo.yyrd.com/artifactory/yonyou-public/com/yonyou/iuap/iuap-2nd-party/8.0.0-SNAPSHOT/maven-metadata.xml
2025-06-27 10:05:26,987 [Worker-29: Building Workspace] INFO  o.e.m.c.i.e.AbstractTransferListenerAdapter - Downloaded https://repo.yyrd.com/artifactory/yonyou-public/com/yonyou/iuap/iuap-2nd-party/8.0.0-SNAPSHOT/maven-metadata.xml
2025-06-27 10:05:27,065 [Worker-29: Building Workspace] INFO  o.e.m.c.i.e.AbstractTransferListenerAdapter - Downloading https://repo.yyrd.com/artifactory/yonyou-public/com/yonyou/iuap/iuap-3rd-party/8.0.0-SNAPSHOT/maven-metadata.xml
2025-06-27 10:05:27,069 [Worker-29: Building Workspace] INFO  o.e.m.c.i.e.AbstractTransferListenerAdapter - Downloaded https://repo.yyrd.com/artifactory/yonyou-public/com/yonyou/iuap/iuap-3rd-party/8.0.0-SNAPSHOT/maven-metadata.xml
2025-06-27 10:05:27,429 [Worker-29: Building Workspace] INFO  o.e.m.c.i.e.AbstractTransferListenerAdapter - Downloading https://repo.yyrd.com/artifactory/yonyou-public/com/yonyou/iuap/iuap-2nd-party/8.1.0-SNAPSHOT/maven-metadata.xml
2025-06-27 10:05:27,433 [Worker-29: Building Workspace] INFO  o.e.m.c.i.e.AbstractTransferListenerAdapter - Downloaded https://repo.yyrd.com/artifactory/yonyou-public/com/yonyou/iuap/iuap-2nd-party/8.1.0-SNAPSHOT/maven-metadata.xml
2025-06-27 10:05:27,499 [Worker-29: Building Workspace] INFO  o.e.m.c.i.e.AbstractTransferListenerAdapter - Downloading https://repo.yyrd.com/artifactory/yonyou-public/com/yonyou/iuap/iuap-3rd-party/8.1.0-SNAPSHOT/maven-metadata.xml
2025-06-27 10:05:27,502 [Worker-29: Building Workspace] INFO  o.e.m.c.i.e.AbstractTransferListenerAdapter - Downloaded https://repo.yyrd.com/artifactory/yonyou-public/com/yonyou/iuap/iuap-3rd-party/8.1.0-SNAPSHOT/maven-metadata.xml
2025-06-27 10:05:28,556 [Worker-29: Building Workspace] INFO  o.e.m.c.i.p.WorkspaceClassifierResolverManager - Resolving P/dev-c-yy-gz-aipoerp-api: com.yonyou.ucf:c-yy-gz-aipoerp-api:ddm-3.0-RELEASE with classifier  to /dev-c-yy-gz-aipoerp-api/target/classes
2025-06-27 10:05:28,558 [Worker-29: Building Workspace] INFO  o.e.m.c.i.p.WorkspaceClassifierResolverManager - Resolving P/dev-c-yy-gz-aipoerp-infrastructure: com.yonyou.ucf:c-yy-gz-aipoerp-infrastructure:ddm-3.0-RELEASE with classifier  to /dev-c-yy-gz-aipoerp-infrastructure/target/classes
2025-06-27 10:05:28,731 [Worker-29: Building Workspace] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-06-27 10:05:28,736 [Worker-29: Building Workspace] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-06-27 10:05:28,978 [Worker-29: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-27 10:05:28,978 [Worker-29: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-27 10:05:28,980 [Worker-29: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-27 10:05:28,981 [Worker-29: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-27 10:05:28,986 [Worker-29: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-27 10:05:28,986 [Worker-29: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-27 10:05:28,986 [Worker-29: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-service\src\test\resources
2025-06-27 10:05:28,989 [Worker-29: Building Workspace] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-06-27 10:05:28,990 [Worker-29: Building Workspace] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-06-27 10:05:30,900 [Worker-29: Building Workspace] INFO  o.e.m.c.i.p.WorkspaceClassifierResolverManager - Resolving P/dev-c-yy-gz-aipoerp-infrastructure: com.yonyou.ucf:c-yy-gz-aipoerp-infrastructure:ddm-3.0-RELEASE with classifier  to /dev-c-yy-gz-aipoerp-infrastructure/target/classes
2025-06-27 10:05:30,982 [Worker-29: Building Workspace] INFO  o.e.m.c.i.p.WorkspaceClassifierResolverManager - Resolving P/dev-c-yy-gz-aipoerp-service: com.yonyou.ucf:c-yy-gz-aipoerp-service:ddm-3.0-RELEASE with classifier  to /dev-c-yy-gz-aipoerp-service/target/classes
2025-06-27 10:05:31,030 [Worker-29: Building Workspace] INFO  o.e.m.c.i.p.WorkspaceClassifierResolverManager - Resolving P/dev-c-yy-gz-aipoerp-api: com.yonyou.ucf:c-yy-gz-aipoerp-api:ddm-3.0-RELEASE with classifier  to /dev-c-yy-gz-aipoerp-api/target/classes
2025-06-27 10:05:31,056 [Worker-29: Building Workspace] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-06-27 10:05:31,057 [Worker-29: Building Workspace] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-06-27 10:05:31,062 [Worker-29: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-27 10:05:31,063 [Worker-29: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-27 10:05:31,063 [Worker-29: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-app\src\main\resources
2025-06-27 10:05:31,063 [Worker-29: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-app\src\main\resources
2025-06-27 10:05:31,066 [Worker-29: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-27 10:05:31,066 [Worker-29: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-27 10:05:31,066 [Worker-29: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-app\src\test\resources
2025-06-27 10:05:31,068 [Worker-29: Building Workspace] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-06-27 10:05:31,069 [Worker-29: Building Workspace] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-06-27 10:05:53,296 [Worker-40: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-27 10:05:53,296 [Worker-40: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-27 10:05:53,296 [Worker-40: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-app\src\main\resources
2025-06-27 10:05:53,297 [Worker-40: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-app\src\main\resources
2025-06-27 10:05:53,301 [Worker-40: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-27 10:05:53,301 [Worker-40: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-27 10:05:53,301 [Worker-40: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-app\src\test\resources
2025-06-27 10:05:54,582 [Worker-40: Building Workspace] INFO  o.e.m.c.i.p.WorkspaceClassifierResolverManager - Resolving P/dev-c-yy-gz-aipoerp-api: com.yonyou.ucf:c-yy-gz-aipoerp-api:ddm-3.0-RELEASE with classifier  to /dev-c-yy-gz-aipoerp-api/target/classes
2025-06-27 10:05:54,583 [Worker-40: Building Workspace] INFO  o.e.m.c.i.p.WorkspaceClassifierResolverManager - Resolving P/dev-c-yy-gz-aipoerp-app: com.yonyou.ucf:c-yy-gz-aipoerp-app:ddm-3.0-RELEASE with classifier  to /dev-c-yy-gz-aipoerp-app/target/classes
2025-06-27 10:05:54,583 [Worker-40: Building Workspace] INFO  o.e.m.c.i.p.WorkspaceClassifierResolverManager - Resolving P/dev-c-yy-gz-aipoerp-infrastructure: com.yonyou.ucf:c-yy-gz-aipoerp-infrastructure:ddm-3.0-RELEASE with classifier  to /dev-c-yy-gz-aipoerp-infrastructure/target/classes
2025-06-27 10:05:54,680 [Worker-40: Building Workspace] INFO  o.e.m.c.i.p.WorkspaceClassifierResolverManager - Resolving P/dev-c-yy-gz-aipoerp-service: com.yonyou.ucf:c-yy-gz-aipoerp-service:ddm-3.0-RELEASE with classifier  to /dev-c-yy-gz-aipoerp-service/target/classes
2025-06-27 10:05:54,781 [Worker-40: Building Workspace] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-06-27 10:05:54,782 [Worker-40: Building Workspace] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-06-27 10:06:03,126 [Worker-41: Push to aipoerp refs/heads/prd - origin] INFO  o.a.s.c.i.DefaultIoServiceFactoryFactory - No detected/configured IoServiceFactoryFactory; using Nio2ServiceFactoryFactory
2025-06-27 10:47:03,558 [Worker-50: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-27 10:47:03,558 [Worker-50: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-27 10:47:03,560 [Worker-50: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-27 10:47:03,561 [Worker-50: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-27 10:47:03,565 [Worker-50: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-27 10:47:03,565 [Worker-50: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-27 10:47:03,565 [Worker-50: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-service\src\test\resources
2025-06-27 10:47:03,738 [Worker-50: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-27 10:47:03,738 [Worker-50: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-27 10:47:03,738 [Worker-50: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-app\src\main\resources
2025-06-27 10:47:03,738 [Worker-50: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-app\src\main\resources
2025-06-27 10:47:03,742 [Worker-50: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-27 10:47:03,742 [Worker-50: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-27 10:47:03,742 [Worker-50: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-app\src\test\resources
2025-06-27 10:47:44,592 [Worker-54: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-27 10:47:44,592 [Worker-54: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-27 10:47:44,592 [Worker-54: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-27 10:47:44,593 [Worker-54: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-27 10:47:44,596 [Worker-54: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-27 10:47:44,596 [Worker-54: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-27 10:47:44,596 [Worker-54: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-service\src\test\resources
2025-06-27 10:49:48,047 [Worker-61: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-27 10:49:48,047 [Worker-61: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-27 10:49:48,047 [Worker-61: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-27 10:49:48,048 [Worker-61: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-27 10:49:48,053 [Worker-61: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-27 10:49:48,053 [Worker-61: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-27 10:49:48,053 [Worker-61: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-service\src\test\resources
2025-06-27 10:50:39,883 [Worker-54: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-27 10:50:39,883 [Worker-54: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-27 10:50:39,883 [Worker-54: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-27 10:50:39,884 [Worker-54: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-27 10:50:39,887 [Worker-54: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-27 10:50:39,887 [Worker-54: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-27 10:50:39,887 [Worker-54: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-service\src\test\resources
2025-06-27 10:51:45,440 [Worker-56: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-27 10:51:45,440 [Worker-56: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-27 10:51:45,440 [Worker-56: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-27 10:51:45,441 [Worker-56: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-27 10:51:45,446 [Worker-56: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-27 10:51:45,446 [Worker-56: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-27 10:51:45,446 [Worker-56: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-service\src\test\resources
2025-06-27 10:52:58,630 [Worker-40: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-27 10:52:58,630 [Worker-40: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-27 10:52:58,630 [Worker-40: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-27 10:52:58,630 [Worker-40: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-27 10:52:58,634 [Worker-40: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-27 10:52:58,634 [Worker-40: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-27 10:52:58,634 [Worker-40: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-service\src\test\resources
2025-06-27 10:53:23,657 [Worker-49: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-27 10:53:23,657 [Worker-49: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-27 10:53:23,657 [Worker-49: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-27 10:53:23,658 [Worker-49: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-27 10:53:23,662 [Worker-49: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-27 10:53:23,662 [Worker-49: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-27 10:53:23,662 [Worker-49: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-service\src\test\resources
2025-06-27 10:53:26,389 [Worker-46: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-27 10:53:26,389 [Worker-46: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-27 10:53:26,389 [Worker-46: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-27 10:53:26,389 [Worker-46: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-27 10:53:26,392 [Worker-46: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-27 10:53:26,392 [Worker-46: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-27 10:53:26,392 [Worker-46: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-service\src\test\resources
2025-06-27 10:53:59,678 [Worker-40: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-27 10:53:59,678 [Worker-40: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-27 10:53:59,678 [Worker-40: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-27 10:53:59,678 [Worker-40: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-27 10:53:59,680 [Worker-40: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-27 10:53:59,681 [Worker-40: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-27 10:53:59,681 [Worker-40: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-service\src\test\resources
2025-06-27 10:59:20,258 [Worker-55: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-27 10:59:20,258 [Worker-55: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-27 10:59:20,258 [Worker-55: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-27 10:59:20,258 [Worker-55: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-27 10:59:20,261 [Worker-55: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-27 10:59:20,261 [Worker-55: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-27 10:59:20,261 [Worker-55: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-service\src\test\resources
2025-06-27 11:02:49,204 [Worker-62: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-27 11:02:49,204 [Worker-62: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-27 11:02:49,204 [Worker-62: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-27 11:02:49,204 [Worker-62: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-27 11:02:49,207 [Worker-62: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-27 11:02:49,207 [Worker-62: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-27 11:02:49,207 [Worker-62: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-service\src\test\resources
2025-06-27 11:03:00,965 [Worker-41: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-27 11:03:00,965 [Worker-41: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-27 11:03:00,965 [Worker-41: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-27 11:03:00,965 [Worker-41: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-27 11:03:00,969 [Worker-41: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-27 11:03:00,969 [Worker-41: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-27 11:03:00,969 [Worker-41: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-service\src\test\resources
2025-06-27 11:03:22,329 [Worker-41: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-27 11:03:22,330 [Worker-41: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-27 11:03:22,330 [Worker-41: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-27 11:03:22,330 [Worker-41: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-27 11:03:22,333 [Worker-41: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-27 11:03:22,333 [Worker-41: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-27 11:03:22,333 [Worker-41: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-service\src\test\resources
2025-06-27 11:03:36,648 [Worker-52: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-27 11:03:36,648 [Worker-52: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-27 11:03:36,648 [Worker-52: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-27 11:03:36,648 [Worker-52: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-27 11:03:36,651 [Worker-52: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-27 11:03:36,651 [Worker-52: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-27 11:03:36,651 [Worker-52: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-service\src\test\resources
2025-06-27 11:04:41,241 [Worker-50: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-27 11:04:41,241 [Worker-50: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-27 11:04:41,241 [Worker-50: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-27 11:04:41,242 [Worker-50: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-27 11:04:41,244 [Worker-50: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-27 11:04:41,244 [Worker-50: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-27 11:04:41,245 [Worker-50: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-service\src\test\resources
2025-06-27 11:05:03,998 [Worker-62: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-27 11:05:03,998 [Worker-62: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-27 11:05:03,998 [Worker-62: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-27 11:05:03,999 [Worker-62: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-27 11:05:04,002 [Worker-62: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-27 11:05:04,002 [Worker-62: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-27 11:05:04,002 [Worker-62: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-service\src\test\resources
2025-06-27 11:05:20,773 [Worker-50: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-27 11:05:20,773 [Worker-50: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-27 11:05:20,773 [Worker-50: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-27 11:05:20,773 [Worker-50: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-27 11:05:20,776 [Worker-50: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-27 11:05:20,776 [Worker-50: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-27 11:05:20,776 [Worker-50: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-service\src\test\resources
2025-06-27 11:08:00,066 [Worker-41: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-27 11:08:00,066 [Worker-41: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-27 11:08:00,066 [Worker-41: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-27 11:08:00,066 [Worker-41: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-27 11:08:00,070 [Worker-41: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-27 11:08:00,070 [Worker-41: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-27 11:08:00,070 [Worker-41: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-service\src\test\resources
2025-06-27 11:08:33,250 [Worker-59: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-27 11:08:33,250 [Worker-59: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-27 11:08:33,251 [Worker-59: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-27 11:08:33,251 [Worker-59: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-27 11:08:33,255 [Worker-59: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-27 11:08:33,255 [Worker-59: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-27 11:08:33,255 [Worker-59: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-service\src\test\resources
2025-06-27 11:09:00,510 [Worker-41: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-27 11:09:00,510 [Worker-41: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-27 11:09:00,510 [Worker-41: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-27 11:09:00,510 [Worker-41: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-27 11:09:00,513 [Worker-41: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-27 11:09:00,513 [Worker-41: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-27 11:09:00,513 [Worker-41: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-service\src\test\resources
2025-06-27 11:09:29,484 [Worker-62: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-27 11:09:29,484 [Worker-62: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-27 11:09:29,484 [Worker-62: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-27 11:09:29,485 [Worker-62: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-27 11:09:29,487 [Worker-62: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-27 11:09:29,487 [Worker-62: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-27 11:09:29,487 [Worker-62: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-service\src\test\resources
2025-06-27 11:10:24,270 [Worker-41: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-27 11:10:24,270 [Worker-41: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-27 11:10:24,270 [Worker-41: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-27 11:10:24,271 [Worker-41: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-27 11:10:24,274 [Worker-41: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-27 11:10:24,274 [Worker-41: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-27 11:10:24,274 [Worker-41: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-service\src\test\resources
2025-06-27 11:11:26,120 [Worker-62: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-27 11:11:26,121 [Worker-62: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-27 11:11:26,121 [Worker-62: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-27 11:11:26,121 [Worker-62: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-27 11:11:26,124 [Worker-62: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-27 11:11:26,124 [Worker-62: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-27 11:11:26,124 [Worker-62: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-service\src\test\resources
2025-06-27 11:11:46,143 [Worker-62: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-27 11:11:46,144 [Worker-62: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-27 11:11:46,144 [Worker-62: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-27 11:11:46,144 [Worker-62: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-27 11:11:46,147 [Worker-62: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-27 11:11:46,147 [Worker-62: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-27 11:11:46,147 [Worker-62: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-service\src\test\resources
2025-06-27 11:11:57,800 [Worker-62: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-27 11:11:57,800 [Worker-62: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-27 11:11:57,801 [Worker-62: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-27 11:11:57,801 [Worker-62: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-27 11:11:57,804 [Worker-62: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-27 11:11:57,804 [Worker-62: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-27 11:11:57,804 [Worker-62: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-service\src\test\resources
2025-06-27 11:12:22,711 [Worker-63: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-27 11:12:22,711 [Worker-63: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-27 11:12:22,711 [Worker-63: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-27 11:12:22,711 [Worker-63: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-27 11:12:22,714 [Worker-63: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-27 11:12:22,715 [Worker-63: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-27 11:12:22,715 [Worker-63: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-service\src\test\resources
2025-06-27 11:12:50,147 [Worker-63: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-27 11:12:50,147 [Worker-63: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-27 11:12:50,147 [Worker-63: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-27 11:12:50,147 [Worker-63: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-27 11:12:50,150 [Worker-63: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-27 11:12:50,150 [Worker-63: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-27 11:12:50,150 [Worker-63: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-service\src\test\resources
2025-06-27 11:13:51,359 [Worker-52: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-27 11:13:51,359 [Worker-52: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-27 11:13:51,359 [Worker-52: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-27 11:13:51,360 [Worker-52: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-06-27 11:13:51,362 [Worker-52: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-06-27 11:13:51,362 [Worker-52: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-06-27 11:13:51,363 [Worker-52: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-service\src\test\resources
2025-06-30 08:44:02,903 [Worker-6: Loading available Gradle versions] INFO  o.e.b.c.i.u.g.PublishedGradleVersions - Gradle version information cache is out-of-date. Trying to update.
2025-06-30 08:44:03,982 [Worker-6: Loading available Gradle versions] INFO  o.e.b.c.i.u.g.PublishedGradleVersions - Updating Gradle version information cache failed. Using outdated cache.
2025-07-01 08:30:47,888 [Worker-5: Loading available Gradle versions] INFO  o.e.b.c.i.u.g.PublishedGradleVersions - Gradle version information cache is out-of-date. Trying to update.
2025-07-02 11:45:46,604 [Worker-1: Loading available Gradle versions] INFO  o.e.b.c.i.u.g.PublishedGradleVersions - Gradle version information cache is out-of-date. Trying to update.
2025-07-02 15:54:34,222 [Worker-43: Building Workspace] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-07-02 15:54:34,236 [Worker-43: Building Workspace] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-07-02 15:54:34,606 [Worker-43: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-07-02 15:54:34,607 [Worker-43: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-07-02 15:54:34,607 [Worker-43: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-infrastructure\src\main\resources
2025-07-02 15:54:34,607 [Worker-43: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-infrastructure\src\main\resources
2025-07-02 15:54:34,614 [Worker-43: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-07-02 15:54:34,614 [Worker-43: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-07-02 15:54:34,614 [Worker-43: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-infrastructure\src\test\resources
2025-07-02 15:54:34,617 [Worker-43: Building Workspace] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-07-02 15:54:34,618 [Worker-43: Building Workspace] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-07-02 15:54:36,328 [Worker-43: Building Workspace] INFO  o.e.m.c.i.p.WorkspaceClassifierResolverManager - Resolving P/dev-c-yy-gz-aipoerp-infrastructure: com.yonyou.ucf:c-yy-gz-aipoerp-infrastructure:ddm-3.0-RELEASE with classifier  to /dev-c-yy-gz-aipoerp-infrastructure/target/classes
2025-07-02 15:54:36,460 [Worker-43: Building Workspace] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-07-02 15:54:36,462 [Worker-43: Building Workspace] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-07-02 15:54:36,467 [Worker-43: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-07-02 15:54:36,468 [Worker-43: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-07-02 15:54:36,468 [Worker-43: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-api\src\main\resources
2025-07-02 15:54:36,468 [Worker-43: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-api\src\main\resources
2025-07-02 15:54:36,471 [Worker-43: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-07-02 15:54:36,472 [Worker-43: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-07-02 15:54:36,472 [Worker-43: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-api\src\test\resources
2025-07-02 15:54:36,473 [Worker-43: Building Workspace] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-07-02 15:54:36,474 [Worker-43: Building Workspace] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-07-02 15:54:39,942 [Worker-43: Building Workspace] INFO  o.e.m.c.i.p.WorkspaceClassifierResolverManager - Resolving P/dev-c-yy-gz-aipoerp-api: com.yonyou.ucf:c-yy-gz-aipoerp-api:ddm-3.0-RELEASE with classifier  to /dev-c-yy-gz-aipoerp-api/target/classes
2025-07-02 15:54:39,943 [Worker-43: Building Workspace] INFO  o.e.m.c.i.p.WorkspaceClassifierResolverManager - Resolving P/dev-c-yy-gz-aipoerp-infrastructure: com.yonyou.ucf:c-yy-gz-aipoerp-infrastructure:ddm-3.0-RELEASE with classifier  to /dev-c-yy-gz-aipoerp-infrastructure/target/classes
2025-07-02 15:54:39,971 [Worker-43: Building Workspace] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-07-02 15:54:39,973 [Worker-43: Building Workspace] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-07-02 15:54:39,982 [Worker-43: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-07-02 15:54:39,982 [Worker-43: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-07-02 15:54:39,985 [Worker-43: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-07-02 15:54:39,985 [Worker-43: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-07-02 15:54:39,989 [Worker-43: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-07-02 15:54:39,989 [Worker-43: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-07-02 15:54:39,989 [Worker-43: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-service\src\test\resources
2025-07-02 15:54:39,990 [Worker-43: Building Workspace] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-07-02 15:54:39,991 [Worker-43: Building Workspace] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-07-02 15:54:41,610 [Worker-43: Building Workspace] INFO  o.e.m.c.i.p.WorkspaceClassifierResolverManager - Resolving P/dev-c-yy-gz-aipoerp-infrastructure: com.yonyou.ucf:c-yy-gz-aipoerp-infrastructure:ddm-3.0-RELEASE with classifier  to /dev-c-yy-gz-aipoerp-infrastructure/target/classes
2025-07-02 15:54:41,689 [Worker-43: Building Workspace] INFO  o.e.m.c.i.p.WorkspaceClassifierResolverManager - Resolving P/dev-c-yy-gz-aipoerp-service: com.yonyou.ucf:c-yy-gz-aipoerp-service:ddm-3.0-RELEASE with classifier  to /dev-c-yy-gz-aipoerp-service/target/classes
2025-07-02 15:54:41,736 [Worker-43: Building Workspace] INFO  o.e.m.c.i.p.WorkspaceClassifierResolverManager - Resolving P/dev-c-yy-gz-aipoerp-api: com.yonyou.ucf:c-yy-gz-aipoerp-api:ddm-3.0-RELEASE with classifier  to /dev-c-yy-gz-aipoerp-api/target/classes
2025-07-02 15:54:41,761 [Worker-43: Building Workspace] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-07-02 15:54:41,762 [Worker-43: Building Workspace] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-07-02 15:54:41,766 [Worker-43: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-07-02 15:54:41,766 [Worker-43: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-07-02 15:54:41,766 [Worker-43: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-app\src\main\resources
2025-07-02 15:54:41,766 [Worker-43: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-app\src\main\resources
2025-07-02 15:54:41,769 [Worker-43: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-07-02 15:54:41,769 [Worker-43: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-07-02 15:54:41,769 [Worker-43: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-app\src\test\resources
2025-07-02 15:54:41,770 [Worker-43: Building Workspace] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-07-02 15:54:41,771 [Worker-43: Building Workspace] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-07-02 15:54:43,228 [Worker-43: Building Workspace] INFO  o.e.m.c.i.p.WorkspaceClassifierResolverManager - Resolving P/dev-c-yy-gz-aipoerp-api: com.yonyou.ucf:c-yy-gz-aipoerp-api:ddm-3.0-RELEASE with classifier  to /dev-c-yy-gz-aipoerp-api/target/classes
2025-07-02 15:54:43,230 [Worker-43: Building Workspace] INFO  o.e.m.c.i.p.WorkspaceClassifierResolverManager - Resolving P/dev-c-yy-gz-aipoerp-app: com.yonyou.ucf:c-yy-gz-aipoerp-app:ddm-3.0-RELEASE with classifier  to /dev-c-yy-gz-aipoerp-app/target/classes
2025-07-02 15:54:43,230 [Worker-43: Building Workspace] INFO  o.e.m.c.i.p.WorkspaceClassifierResolverManager - Resolving P/dev-c-yy-gz-aipoerp-infrastructure: com.yonyou.ucf:c-yy-gz-aipoerp-infrastructure:ddm-3.0-RELEASE with classifier  to /dev-c-yy-gz-aipoerp-infrastructure/target/classes
2025-07-02 15:54:43,310 [Worker-43: Building Workspace] INFO  o.e.m.c.i.p.WorkspaceClassifierResolverManager - Resolving P/dev-c-yy-gz-aipoerp-service: com.yonyou.ucf:c-yy-gz-aipoerp-service:ddm-3.0-RELEASE with classifier  to /dev-c-yy-gz-aipoerp-service/target/classes
2025-07-02 15:54:43,407 [Worker-43: Building Workspace] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-07-02 15:54:43,409 [Worker-43: Building Workspace] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-07-02 15:54:43,425 [Worker-43: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-07-02 15:54:43,425 [Worker-43: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-07-02 15:54:43,426 [Worker-43: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 13 resources
2025-07-02 15:54:43,464 [Worker-43: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 2 resources
2025-07-02 15:54:43,474 [Worker-43: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-07-02 15:54:43,474 [Worker-43: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-07-02 15:54:43,474 [Worker-43: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-bootstrap\src\test\resources
2025-07-02 15:54:43,488 [Worker-43: Building Workspace] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-07-02 15:54:43,489 [Worker-43: Building Workspace] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-07-02 15:55:54,958 [Worker-28: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-07-02 15:55:54,958 [Worker-28: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-07-02 15:55:54,959 [Worker-28: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-07-02 15:55:54,960 [Worker-28: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-07-02 15:55:54,963 [Worker-28: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-07-02 15:55:54,963 [Worker-28: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-07-02 15:55:54,963 [Worker-28: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-service\src\test\resources
2025-07-02 16:07:10,776 [Worker-53: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-07-02 16:07:10,777 [Worker-53: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-07-02 16:07:10,777 [Worker-53: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-07-02 16:07:10,778 [Worker-53: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-07-02 16:07:10,780 [Worker-53: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-07-02 16:07:10,780 [Worker-53: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-07-02 16:07:10,780 [Worker-53: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-service\src\test\resources
2025-07-02 16:07:40,462 [Worker-42: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-07-02 16:07:40,462 [Worker-42: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-07-02 16:07:40,463 [Worker-42: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-07-02 16:07:40,463 [Worker-42: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-07-02 16:07:40,465 [Worker-42: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-07-02 16:07:40,466 [Worker-42: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-07-02 16:07:40,466 [Worker-42: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-service\src\test\resources
2025-07-02 16:07:56,698 [Worker-51: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-07-02 16:07:56,699 [Worker-51: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-07-02 16:07:56,699 [Worker-51: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-07-02 16:07:56,699 [Worker-51: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-07-02 16:07:56,702 [Worker-51: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-07-02 16:07:56,702 [Worker-51: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-07-02 16:07:56,702 [Worker-51: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-service\src\test\resources
2025-07-02 16:17:58,250 [Worker-42: Push to aipoerp refs/heads/prd - origin] INFO  o.a.s.c.i.DefaultIoServiceFactoryFactory - No detected/configured IoServiceFactoryFactory; using Nio2ServiceFactoryFactory
2025-07-03 08:52:24,350 [Worker-4: Loading available Gradle versions] INFO  o.e.b.c.i.u.g.PublishedGradleVersions - Gradle version information cache is up-to-date. Trying to read.
2025-07-03 09:13:37,812 [Worker-41: Building Workspace] INFO  o.e.m.c.i.e.AbstractTransferListenerAdapter - Downloading https://repo.yyrd.com/artifactory/yonyou-public/com/yonyou/iuap/iuap-2nd-party/8.0.0-SNAPSHOT/maven-metadata.xml
2025-07-03 09:13:37,836 [Worker-41: Building Workspace] INFO  o.e.m.c.i.e.AbstractTransferListenerAdapter - Downloaded https://repo.yyrd.com/artifactory/yonyou-public/com/yonyou/iuap/iuap-2nd-party/8.0.0-SNAPSHOT/maven-metadata.xml
2025-07-03 09:13:37,934 [Worker-41: Building Workspace] INFO  o.e.m.c.i.e.AbstractTransferListenerAdapter - Downloading https://repo.yyrd.com/artifactory/yonyou-public/com/yonyou/iuap/iuap-3rd-party/8.0.0-SNAPSHOT/maven-metadata.xml
2025-07-03 09:13:37,938 [Worker-41: Building Workspace] INFO  o.e.m.c.i.e.AbstractTransferListenerAdapter - Downloaded https://repo.yyrd.com/artifactory/yonyou-public/com/yonyou/iuap/iuap-3rd-party/8.0.0-SNAPSHOT/maven-metadata.xml
2025-07-03 09:13:38,108 [Worker-41: Building Workspace] INFO  o.e.m.c.i.e.AbstractTransferListenerAdapter - Downloading https://repo.yyrd.com/artifactory/yonyou-public/com/yonyou/iuap/iuap-2nd-party/8.1.0-SNAPSHOT/maven-metadata.xml
2025-07-03 09:13:38,112 [Worker-41: Building Workspace] INFO  o.e.m.c.i.e.AbstractTransferListenerAdapter - Downloaded https://repo.yyrd.com/artifactory/yonyou-public/com/yonyou/iuap/iuap-2nd-party/8.1.0-SNAPSHOT/maven-metadata.xml
2025-07-03 09:13:38,176 [Worker-41: Building Workspace] INFO  o.e.m.c.i.e.AbstractTransferListenerAdapter - Downloading https://repo.yyrd.com/artifactory/yonyou-public/com/yonyou/iuap/iuap-3rd-party/8.1.0-SNAPSHOT/maven-metadata.xml
2025-07-03 09:13:38,179 [Worker-41: Building Workspace] INFO  o.e.m.c.i.e.AbstractTransferListenerAdapter - Downloaded https://repo.yyrd.com/artifactory/yonyou-public/com/yonyou/iuap/iuap-3rd-party/8.1.0-SNAPSHOT/maven-metadata.xml
2025-07-03 09:13:39,250 [Worker-41: Building Workspace] INFO  o.e.m.c.i.p.WorkspaceClassifierResolverManager - Resolving P/dev-c-yy-gz-aipoerp-api: com.yonyou.ucf:c-yy-gz-aipoerp-api:ddm-3.0-RELEASE with classifier  to /dev-c-yy-gz-aipoerp-api/target/classes
2025-07-03 09:13:39,252 [Worker-41: Building Workspace] INFO  o.e.m.c.i.p.WorkspaceClassifierResolverManager - Resolving P/dev-c-yy-gz-aipoerp-infrastructure: com.yonyou.ucf:c-yy-gz-aipoerp-infrastructure:ddm-3.0-RELEASE with classifier  to /dev-c-yy-gz-aipoerp-infrastructure/target/classes
2025-07-03 09:13:39,455 [Worker-41: Building Workspace] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-07-03 09:13:39,462 [Worker-41: Building Workspace] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-07-03 09:13:39,800 [Worker-41: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-07-03 09:13:39,801 [Worker-41: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-07-03 09:13:39,805 [Worker-41: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-07-03 09:13:39,805 [Worker-41: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-07-03 09:13:39,812 [Worker-41: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-07-03 09:13:39,813 [Worker-41: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-07-03 09:13:39,813 [Worker-41: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-service\src\test\resources
2025-07-03 09:13:39,817 [Worker-41: Building Workspace] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-07-03 09:13:39,818 [Worker-41: Building Workspace] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-07-03 09:15:23,928 [Worker-14: Push to aipoerp refs/heads/prd - origin] INFO  o.a.s.c.i.DefaultIoServiceFactoryFactory - No detected/configured IoServiceFactoryFactory; using Nio2ServiceFactoryFactory
2025-07-03 15:44:23,589 [Worker-61: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-07-03 15:44:23,590 [Worker-61: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-07-03 15:44:23,592 [Worker-61: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-07-03 15:44:23,593 [Worker-61: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-07-03 15:44:23,598 [Worker-61: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-07-03 15:44:23,598 [Worker-61: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-07-03 15:44:23,598 [Worker-61: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-service\src\test\resources
2025-07-03 15:44:25,832 [Worker-61: Building Workspace] INFO  o.e.m.c.i.p.WorkspaceClassifierResolverManager - Resolving P/dev-c-yy-gz-aipoerp-api: com.yonyou.ucf:c-yy-gz-aipoerp-api:ddm-3.0-RELEASE with classifier  to /dev-c-yy-gz-aipoerp-api/target/classes
2025-07-03 15:44:25,833 [Worker-61: Building Workspace] INFO  o.e.m.c.i.p.WorkspaceClassifierResolverManager - Resolving P/dev-c-yy-gz-aipoerp-app: com.yonyou.ucf:c-yy-gz-aipoerp-app:ddm-3.0-RELEASE with classifier  to /dev-c-yy-gz-aipoerp-app/target/classes
2025-07-03 15:44:25,833 [Worker-61: Building Workspace] INFO  o.e.m.c.i.p.WorkspaceClassifierResolverManager - Resolving P/dev-c-yy-gz-aipoerp-infrastructure: com.yonyou.ucf:c-yy-gz-aipoerp-infrastructure:ddm-3.0-RELEASE with classifier  to /dev-c-yy-gz-aipoerp-infrastructure/target/classes
2025-07-03 15:44:25,925 [Worker-61: Building Workspace] INFO  o.e.m.c.i.p.WorkspaceClassifierResolverManager - Resolving P/dev-c-yy-gz-aipoerp-service: com.yonyou.ucf:c-yy-gz-aipoerp-service:ddm-3.0-RELEASE with classifier  to /dev-c-yy-gz-aipoerp-service/target/classes
2025-07-03 15:44:26,037 [Worker-61: Building Workspace] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-07-03 15:44:26,039 [Worker-61: Building Workspace] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-07-03 15:44:39,696 [Worker-58: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-07-03 15:44:39,697 [Worker-58: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-07-03 15:44:39,697 [Worker-58: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-07-03 15:44:39,697 [Worker-58: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-07-03 15:44:39,701 [Worker-58: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-07-03 15:44:39,701 [Worker-58: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-07-03 15:44:39,701 [Worker-58: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-service\src\test\resources
2025-07-03 15:46:03,272 [Worker-58: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-07-03 15:46:03,272 [Worker-58: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-07-03 15:46:03,273 [Worker-58: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-07-03 15:46:03,273 [Worker-58: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-07-03 15:46:03,277 [Worker-58: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-07-03 15:46:03,277 [Worker-58: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-07-03 15:46:03,277 [Worker-58: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-service\src\test\resources
2025-07-04 08:49:18,749 [Worker-5: Loading available Gradle versions] INFO  o.e.b.c.i.u.g.PublishedGradleVersions - Gradle version information cache is out-of-date. Trying to update.
2025-07-08 08:45:36,166 [Worker-6: Loading available Gradle versions] INFO  o.e.b.c.i.u.g.PublishedGradleVersions - Gradle version information cache is out-of-date. Trying to update.
2025-07-08 09:55:00,512 [Worker-7: Fetching remote <NAME_EMAIL>:005/005/aipogroup/aipor6/aipoerp.git] INFO  o.a.s.c.i.DefaultIoServiceFactoryFactory - No detected/configured IoServiceFactoryFactory; using Nio2ServiceFactoryFactory
2025-07-09 08:59:57,133 [Worker-6: Loading available Gradle versions] INFO  o.e.b.c.i.u.g.PublishedGradleVersions - Gradle version information cache is out-of-date. Trying to update.
2025-07-09 20:14:50,526 [Worker-55: Building Workspace] INFO  o.e.m.c.i.e.AbstractTransferListenerAdapter - Downloading https://repo.yyrd.com/artifactory/yonyou-public/com/yonyou/iuap/iuap-2nd-party/8.0.0-SNAPSHOT/maven-metadata.xml
2025-07-09 20:14:50,544 [Worker-55: Building Workspace] INFO  o.e.m.c.i.e.AbstractTransferListenerAdapter - Downloaded https://repo.yyrd.com/artifactory/yonyou-public/com/yonyou/iuap/iuap-2nd-party/8.0.0-SNAPSHOT/maven-metadata.xml
2025-07-09 20:14:50,621 [Worker-55: Building Workspace] INFO  o.e.m.c.i.e.AbstractTransferListenerAdapter - Downloading https://repo.yyrd.com/artifactory/yonyou-public/com/yonyou/iuap/iuap-3rd-party/8.0.0-SNAPSHOT/maven-metadata.xml
2025-07-09 20:14:50,624 [Worker-55: Building Workspace] INFO  o.e.m.c.i.e.AbstractTransferListenerAdapter - Downloaded https://repo.yyrd.com/artifactory/yonyou-public/com/yonyou/iuap/iuap-3rd-party/8.0.0-SNAPSHOT/maven-metadata.xml
2025-07-09 20:14:50,790 [Worker-55: Building Workspace] INFO  o.e.m.c.i.e.AbstractTransferListenerAdapter - Downloading https://repo.yyrd.com/artifactory/yonyou-public/com/yonyou/iuap/iuap-2nd-party/8.1.0-SNAPSHOT/maven-metadata.xml
2025-07-09 20:14:50,794 [Worker-55: Building Workspace] INFO  o.e.m.c.i.e.AbstractTransferListenerAdapter - Downloaded https://repo.yyrd.com/artifactory/yonyou-public/com/yonyou/iuap/iuap-2nd-party/8.1.0-SNAPSHOT/maven-metadata.xml
2025-07-09 20:14:50,856 [Worker-55: Building Workspace] INFO  o.e.m.c.i.e.AbstractTransferListenerAdapter - Downloading https://repo.yyrd.com/artifactory/yonyou-public/com/yonyou/iuap/iuap-3rd-party/8.1.0-SNAPSHOT/maven-metadata.xml
2025-07-09 20:14:50,862 [Worker-55: Building Workspace] INFO  o.e.m.c.i.e.AbstractTransferListenerAdapter - Downloaded https://repo.yyrd.com/artifactory/yonyou-public/com/yonyou/iuap/iuap-3rd-party/8.1.0-SNAPSHOT/maven-metadata.xml
2025-07-09 20:14:52,129 [Worker-55: Building Workspace] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-07-09 20:14:52,139 [Worker-55: Building Workspace] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-07-09 20:14:52,608 [Worker-55: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-07-09 20:14:52,608 [Worker-55: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-07-09 20:14:52,608 [Worker-55: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-infrastructure\src\main\resources
2025-07-09 20:14:52,608 [Worker-55: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-infrastructure\src\main\resources
2025-07-09 20:14:52,614 [Worker-55: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-07-09 20:14:52,614 [Worker-55: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-07-09 20:14:52,615 [Worker-55: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-infrastructure\src\test\resources
2025-07-09 20:14:52,618 [Worker-55: Building Workspace] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-07-09 20:14:52,619 [Worker-55: Building Workspace] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-07-09 20:14:54,693 [Worker-55: Building Workspace] INFO  o.e.m.c.i.p.WorkspaceClassifierResolverManager - Resolving P/dev-c-yy-gz-aipoerp-infrastructure: com.yonyou.ucf:c-yy-gz-aipoerp-infrastructure:ddm-3.0-RELEASE with classifier  to /dev-c-yy-gz-aipoerp-infrastructure/target/classes
2025-07-09 20:14:54,861 [Worker-55: Building Workspace] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-07-09 20:14:54,863 [Worker-55: Building Workspace] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-07-09 20:14:54,870 [Worker-55: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-07-09 20:14:54,870 [Worker-55: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-07-09 20:14:54,870 [Worker-55: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-api\src\main\resources
2025-07-09 20:14:54,870 [Worker-55: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-api\src\main\resources
2025-07-09 20:14:54,874 [Worker-55: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-07-09 20:14:54,875 [Worker-55: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-07-09 20:14:54,875 [Worker-55: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-api\src\test\resources
2025-07-09 20:14:54,876 [Worker-55: Building Workspace] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-07-09 20:14:54,877 [Worker-55: Building Workspace] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-07-09 20:14:58,604 [Worker-55: Building Workspace] INFO  o.e.m.c.i.p.WorkspaceClassifierResolverManager - Resolving P/dev-c-yy-gz-aipoerp-api: com.yonyou.ucf:c-yy-gz-aipoerp-api:ddm-3.0-RELEASE with classifier  to /dev-c-yy-gz-aipoerp-api/target/classes
2025-07-09 20:14:58,605 [Worker-55: Building Workspace] INFO  o.e.m.c.i.p.WorkspaceClassifierResolverManager - Resolving P/dev-c-yy-gz-aipoerp-infrastructure: com.yonyou.ucf:c-yy-gz-aipoerp-infrastructure:ddm-3.0-RELEASE with classifier  to /dev-c-yy-gz-aipoerp-infrastructure/target/classes
2025-07-09 20:14:58,644 [Worker-55: Building Workspace] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-07-09 20:14:58,646 [Worker-55: Building Workspace] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-07-09 20:14:58,657 [Worker-55: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-07-09 20:14:58,657 [Worker-55: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-07-09 20:14:58,672 [Worker-55: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-07-09 20:14:58,674 [Worker-55: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-07-09 20:14:58,677 [Worker-55: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-07-09 20:14:58,677 [Worker-55: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-07-09 20:14:58,677 [Worker-55: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-service\src\test\resources
2025-07-09 20:14:58,679 [Worker-55: Building Workspace] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-07-09 20:14:58,680 [Worker-55: Building Workspace] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-07-09 20:15:00,213 [Worker-55: Building Workspace] INFO  o.e.m.c.i.p.WorkspaceClassifierResolverManager - Resolving P/dev-c-yy-gz-aipoerp-infrastructure: com.yonyou.ucf:c-yy-gz-aipoerp-infrastructure:ddm-3.0-RELEASE with classifier  to /dev-c-yy-gz-aipoerp-infrastructure/target/classes
2025-07-09 20:15:00,286 [Worker-55: Building Workspace] INFO  o.e.m.c.i.p.WorkspaceClassifierResolverManager - Resolving P/dev-c-yy-gz-aipoerp-service: com.yonyou.ucf:c-yy-gz-aipoerp-service:ddm-3.0-RELEASE with classifier  to /dev-c-yy-gz-aipoerp-service/target/classes
2025-07-09 20:15:00,335 [Worker-55: Building Workspace] INFO  o.e.m.c.i.p.WorkspaceClassifierResolverManager - Resolving P/dev-c-yy-gz-aipoerp-api: com.yonyou.ucf:c-yy-gz-aipoerp-api:ddm-3.0-RELEASE with classifier  to /dev-c-yy-gz-aipoerp-api/target/classes
2025-07-09 20:15:00,359 [Worker-55: Building Workspace] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-07-09 20:15:00,361 [Worker-55: Building Workspace] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-07-09 20:15:00,365 [Worker-55: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-07-09 20:15:00,365 [Worker-55: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-07-09 20:15:00,365 [Worker-55: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-app\src\main\resources
2025-07-09 20:15:00,365 [Worker-55: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-app\src\main\resources
2025-07-09 20:15:00,368 [Worker-55: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-07-09 20:15:00,368 [Worker-55: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-07-09 20:15:00,368 [Worker-55: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-app\src\test\resources
2025-07-09 20:15:00,370 [Worker-55: Building Workspace] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-07-09 20:15:00,372 [Worker-55: Building Workspace] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-07-09 20:15:02,067 [Worker-55: Building Workspace] INFO  o.e.m.c.i.p.WorkspaceClassifierResolverManager - Resolving P/dev-c-yy-gz-aipoerp-api: com.yonyou.ucf:c-yy-gz-aipoerp-api:ddm-3.0-RELEASE with classifier  to /dev-c-yy-gz-aipoerp-api/target/classes
2025-07-09 20:15:02,069 [Worker-55: Building Workspace] INFO  o.e.m.c.i.p.WorkspaceClassifierResolverManager - Resolving P/dev-c-yy-gz-aipoerp-app: com.yonyou.ucf:c-yy-gz-aipoerp-app:ddm-3.0-RELEASE with classifier  to /dev-c-yy-gz-aipoerp-app/target/classes
2025-07-09 20:15:02,069 [Worker-55: Building Workspace] INFO  o.e.m.c.i.p.WorkspaceClassifierResolverManager - Resolving P/dev-c-yy-gz-aipoerp-infrastructure: com.yonyou.ucf:c-yy-gz-aipoerp-infrastructure:ddm-3.0-RELEASE with classifier  to /dev-c-yy-gz-aipoerp-infrastructure/target/classes
2025-07-09 20:15:02,154 [Worker-55: Building Workspace] INFO  o.e.m.c.i.p.WorkspaceClassifierResolverManager - Resolving P/dev-c-yy-gz-aipoerp-service: com.yonyou.ucf:c-yy-gz-aipoerp-service:ddm-3.0-RELEASE with classifier  to /dev-c-yy-gz-aipoerp-service/target/classes
2025-07-09 20:15:02,275 [Worker-55: Building Workspace] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-07-09 20:15:02,277 [Worker-55: Building Workspace] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-07-09 20:15:02,301 [Worker-55: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-07-09 20:15:02,301 [Worker-55: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-07-09 20:15:02,302 [Worker-55: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 13 resources
2025-07-09 20:15:02,371 [Worker-55: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 2 resources
2025-07-09 20:15:02,381 [Worker-55: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-07-09 20:15:02,382 [Worker-55: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-07-09 20:15:02,382 [Worker-55: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-bootstrap\src\test\resources
2025-07-09 20:15:02,396 [Worker-55: Building Workspace] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-07-09 20:15:02,397 [Worker-55: Building Workspace] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-07-09 20:19:59,133 [Worker-59: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-07-09 20:19:59,133 [Worker-59: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-07-09 20:19:59,137 [Worker-59: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-07-09 20:19:59,137 [Worker-59: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-07-09 20:19:59,143 [Worker-59: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-07-09 20:19:59,143 [Worker-59: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-07-09 20:19:59,143 [Worker-59: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-service\src\test\resources
2025-07-09 20:20:13,304 [Worker-59: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-07-09 20:20:13,304 [Worker-59: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-07-09 20:20:13,305 [Worker-59: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-07-09 20:20:13,305 [Worker-59: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-07-09 20:20:13,309 [Worker-59: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-07-09 20:20:13,309 [Worker-59: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-07-09 20:20:13,310 [Worker-59: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-service\src\test\resources
2025-07-09 20:20:40,746 [Worker-56: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-07-09 20:20:40,746 [Worker-56: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-07-09 20:20:40,747 [Worker-56: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-07-09 20:20:40,747 [Worker-56: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-07-09 20:20:40,750 [Worker-56: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-07-09 20:20:40,750 [Worker-56: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-07-09 20:20:40,750 [Worker-56: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-service\src\test\resources
2025-07-09 20:21:02,025 [Worker-55: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-07-09 20:21:02,025 [Worker-55: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-07-09 20:21:02,026 [Worker-55: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-07-09 20:21:02,026 [Worker-55: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-07-09 20:21:02,031 [Worker-55: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-07-09 20:21:02,031 [Worker-55: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-07-09 20:21:02,031 [Worker-55: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-service\src\test\resources
2025-07-09 20:21:47,600 [Worker-57: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-07-09 20:21:47,600 [Worker-57: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-07-09 20:21:47,600 [Worker-57: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-07-09 20:21:47,601 [Worker-57: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-07-09 20:21:47,604 [Worker-57: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-07-09 20:21:47,604 [Worker-57: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-07-09 20:21:47,604 [Worker-57: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-service\src\test\resources
2025-07-09 20:24:52,906 [Worker-48: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-07-09 20:24:52,906 [Worker-48: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-07-09 20:24:52,906 [Worker-48: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-07-09 20:24:52,906 [Worker-48: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-07-09 20:24:52,910 [Worker-48: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-07-09 20:24:52,910 [Worker-48: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-07-09 20:24:52,910 [Worker-48: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-service\src\test\resources
2025-07-09 20:26:23,027 [Worker-58: Push to aipoerp refs/heads/prd - origin] INFO  o.a.s.c.i.DefaultIoServiceFactoryFactory - No detected/configured IoServiceFactoryFactory; using Nio2ServiceFactoryFactory
2025-07-10 08:45:26,517 [Worker-4: Loading available Gradle versions] INFO  o.e.b.c.i.u.g.PublishedGradleVersions - Gradle version information cache is up-to-date. Trying to read.
2025-07-10 09:02:32,324 [Worker-39: Building Workspace] INFO  o.e.m.c.i.e.AbstractTransferListenerAdapter - Downloading https://repo.yyrd.com/artifactory/yonyou-public/com/yonyou/iuap/iuap-2nd-party/8.0.0-SNAPSHOT/maven-metadata.xml
2025-07-10 09:02:32,332 [Worker-39: Building Workspace] INFO  o.e.m.c.i.e.AbstractTransferListenerAdapter - Downloaded https://repo.yyrd.com/artifactory/yonyou-public/com/yonyou/iuap/iuap-2nd-party/8.0.0-SNAPSHOT/maven-metadata.xml
2025-07-10 09:02:32,404 [Worker-39: Building Workspace] INFO  o.e.m.c.i.e.AbstractTransferListenerAdapter - Downloading https://repo.yyrd.com/artifactory/yonyou-public/com/yonyou/iuap/iuap-3rd-party/8.0.0-SNAPSHOT/maven-metadata.xml
2025-07-10 09:02:32,406 [Worker-39: Building Workspace] INFO  o.e.m.c.i.e.AbstractTransferListenerAdapter - Downloaded https://repo.yyrd.com/artifactory/yonyou-public/com/yonyou/iuap/iuap-3rd-party/8.0.0-SNAPSHOT/maven-metadata.xml
2025-07-10 09:02:32,568 [Worker-39: Building Workspace] INFO  o.e.m.c.i.e.AbstractTransferListenerAdapter - Downloading https://repo.yyrd.com/artifactory/yonyou-public/com/yonyou/iuap/iuap-2nd-party/8.1.0-SNAPSHOT/maven-metadata.xml
2025-07-10 09:02:32,571 [Worker-39: Building Workspace] INFO  o.e.m.c.i.e.AbstractTransferListenerAdapter - Downloaded https://repo.yyrd.com/artifactory/yonyou-public/com/yonyou/iuap/iuap-2nd-party/8.1.0-SNAPSHOT/maven-metadata.xml
2025-07-10 09:02:32,634 [Worker-39: Building Workspace] INFO  o.e.m.c.i.e.AbstractTransferListenerAdapter - Downloading https://repo.yyrd.com/artifactory/yonyou-public/com/yonyou/iuap/iuap-3rd-party/8.1.0-SNAPSHOT/maven-metadata.xml
2025-07-10 09:02:32,636 [Worker-39: Building Workspace] INFO  o.e.m.c.i.e.AbstractTransferListenerAdapter - Downloaded https://repo.yyrd.com/artifactory/yonyou-public/com/yonyou/iuap/iuap-3rd-party/8.1.0-SNAPSHOT/maven-metadata.xml
2025-07-10 09:02:33,645 [Worker-39: Building Workspace] INFO  o.e.m.c.i.p.WorkspaceClassifierResolverManager - Resolving P/dev-c-yy-gz-aipoerp-api: com.yonyou.ucf:c-yy-gz-aipoerp-api:ddm-3.0-RELEASE with classifier  to /dev-c-yy-gz-aipoerp-api/target/classes
2025-07-10 09:02:33,647 [Worker-39: Building Workspace] INFO  o.e.m.c.i.p.WorkspaceClassifierResolverManager - Resolving P/dev-c-yy-gz-aipoerp-infrastructure: com.yonyou.ucf:c-yy-gz-aipoerp-infrastructure:ddm-3.0-RELEASE with classifier  to /dev-c-yy-gz-aipoerp-infrastructure/target/classes
2025-07-10 09:02:33,825 [Worker-39: Building Workspace] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-07-10 09:02:33,829 [Worker-39: Building Workspace] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-07-10 09:02:34,136 [Worker-39: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-07-10 09:02:34,136 [Worker-39: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-07-10 09:02:34,143 [Worker-39: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-07-10 09:02:34,144 [Worker-39: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-07-10 09:02:34,148 [Worker-39: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-07-10 09:02:34,148 [Worker-39: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-07-10 09:02:34,148 [Worker-39: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-service\src\test\resources
2025-07-10 09:02:34,152 [Worker-39: Building Workspace] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-07-10 09:02:34,153 [Worker-39: Building Workspace] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-07-10 09:10:36,950 [Worker-38: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-07-10 09:10:36,951 [Worker-38: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-07-10 09:10:36,952 [Worker-38: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-07-10 09:10:36,952 [Worker-38: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-07-10 09:10:36,955 [Worker-38: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-07-10 09:10:36,955 [Worker-38: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-07-10 09:10:36,955 [Worker-38: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-service\src\test\resources
2025-07-10 09:10:38,524 [Worker-38: Building Workspace] INFO  o.e.m.c.i.p.WorkspaceClassifierResolverManager - Resolving P/dev-c-yy-gz-aipoerp-api: com.yonyou.ucf:c-yy-gz-aipoerp-api:ddm-3.0-RELEASE with classifier  to /dev-c-yy-gz-aipoerp-api/target/classes
2025-07-10 09:10:38,526 [Worker-38: Building Workspace] INFO  o.e.m.c.i.p.WorkspaceClassifierResolverManager - Resolving P/dev-c-yy-gz-aipoerp-app: com.yonyou.ucf:c-yy-gz-aipoerp-app:ddm-3.0-RELEASE with classifier  to /dev-c-yy-gz-aipoerp-app/target/classes
2025-07-10 09:10:38,526 [Worker-38: Building Workspace] INFO  o.e.m.c.i.p.WorkspaceClassifierResolverManager - Resolving P/dev-c-yy-gz-aipoerp-infrastructure: com.yonyou.ucf:c-yy-gz-aipoerp-infrastructure:ddm-3.0-RELEASE with classifier  to /dev-c-yy-gz-aipoerp-infrastructure/target/classes
2025-07-10 09:10:38,600 [Worker-38: Building Workspace] INFO  o.e.m.c.i.p.WorkspaceClassifierResolverManager - Resolving P/dev-c-yy-gz-aipoerp-service: com.yonyou.ucf:c-yy-gz-aipoerp-service:ddm-3.0-RELEASE with classifier  to /dev-c-yy-gz-aipoerp-service/target/classes
2025-07-10 09:10:38,703 [Worker-38: Building Workspace] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-07-10 09:10:38,705 [Worker-38: Building Workspace] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-07-10 09:12:25,405 [Worker-44: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-07-10 09:12:25,405 [Worker-44: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-07-10 09:12:25,405 [Worker-44: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-07-10 09:12:25,406 [Worker-44: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-07-10 09:12:25,409 [Worker-44: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-07-10 09:12:25,409 [Worker-44: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-07-10 09:12:25,409 [Worker-44: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-service\src\test\resources
2025-07-10 09:12:53,157 [Worker-43: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-07-10 09:12:53,158 [Worker-43: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-07-10 09:12:53,158 [Worker-43: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-07-10 09:12:53,158 [Worker-43: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-07-10 09:12:53,161 [Worker-43: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-07-10 09:12:53,161 [Worker-43: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-07-10 09:12:53,161 [Worker-43: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-service\src\test\resources
2025-07-10 09:13:58,619 [Worker-44: Push to aipoerp refs/heads/prd - origin] INFO  o.a.s.c.i.DefaultIoServiceFactoryFactory - No detected/configured IoServiceFactoryFactory; using Nio2ServiceFactoryFactory
2025-07-11 09:06:21,630 [Worker-6: Loading available Gradle versions] INFO  o.e.b.c.i.u.g.PublishedGradleVersions - Gradle version information cache is out-of-date. Trying to update.
2025-07-14 08:59:14,472 [Worker-4: Loading available Gradle versions] INFO  o.e.b.c.i.u.g.PublishedGradleVersions - Gradle version information cache is out-of-date. Trying to update.
2025-07-14 15:29:11,951 [Worker-58: Download sources and javadoc] INFO  o.e.m.c.i.p.WorkspaceClassifierResolverManager - Resolving P/dev-c-yy-gz-aipoerp-api: com.yonyou.ucf:c-yy-gz-aipoerp-api:ddm-3.0-RELEASE with classifier  to /dev-c-yy-gz-aipoerp-api/target/classes
2025-07-14 15:29:11,958 [Worker-58: Download sources and javadoc] INFO  o.e.m.c.i.p.WorkspaceClassifierResolverManager - Resolving P/dev-c-yy-gz-aipoerp-infrastructure: com.yonyou.ucf:c-yy-gz-aipoerp-infrastructure:ddm-3.0-RELEASE with classifier  to /dev-c-yy-gz-aipoerp-infrastructure/target/classes
2025-07-14 15:37:21,807 [Worker-58: Building Workspace] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-07-14 15:37:21,813 [Worker-58: Building Workspace] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-07-14 15:37:22,056 [Worker-58: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-07-14 15:37:22,057 [Worker-58: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-07-14 15:37:22,057 [Worker-58: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-infrastructure\src\main\resources
2025-07-14 15:37:22,057 [Worker-58: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-infrastructure\src\main\resources
2025-07-14 15:37:22,063 [Worker-58: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-07-14 15:37:22,063 [Worker-58: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-07-14 15:37:22,063 [Worker-58: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-infrastructure\src\test\resources
2025-07-14 15:37:22,067 [Worker-58: Building Workspace] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-07-14 15:37:22,069 [Worker-58: Building Workspace] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-07-14 15:37:23,452 [Worker-58: Building Workspace] INFO  o.e.m.c.i.p.WorkspaceClassifierResolverManager - Resolving P/dev-c-yy-gz-aipoerp-infrastructure: com.yonyou.ucf:c-yy-gz-aipoerp-infrastructure:ddm-3.0-RELEASE with classifier  to /dev-c-yy-gz-aipoerp-infrastructure/target/classes
2025-07-14 15:37:23,567 [Worker-58: Building Workspace] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-07-14 15:37:23,568 [Worker-58: Building Workspace] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-07-14 15:37:23,580 [Worker-58: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-07-14 15:37:23,580 [Worker-58: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-07-14 15:37:23,580 [Worker-58: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-api\src\main\resources
2025-07-14 15:37:23,580 [Worker-58: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-api\src\main\resources
2025-07-14 15:37:23,583 [Worker-58: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-07-14 15:37:23,583 [Worker-58: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-07-14 15:37:23,583 [Worker-58: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-api\src\test\resources
2025-07-14 15:37:23,584 [Worker-58: Building Workspace] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-07-14 15:37:23,585 [Worker-58: Building Workspace] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-07-14 15:37:25,391 [Worker-58: Building Workspace] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-07-14 15:37:25,394 [Worker-58: Building Workspace] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-07-14 15:37:25,406 [Worker-58: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-07-14 15:37:25,406 [Worker-58: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-07-14 15:37:25,421 [Worker-58: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-07-14 15:37:25,424 [Worker-58: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-07-14 15:37:25,427 [Worker-58: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-07-14 15:37:25,427 [Worker-58: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-07-14 15:37:25,427 [Worker-58: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-service\src\test\resources
2025-07-14 15:37:25,429 [Worker-58: Building Workspace] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-07-14 15:37:25,430 [Worker-58: Building Workspace] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-07-14 15:37:27,084 [Worker-58: Building Workspace] INFO  o.e.m.c.i.p.WorkspaceClassifierResolverManager - Resolving P/dev-c-yy-gz-aipoerp-infrastructure: com.yonyou.ucf:c-yy-gz-aipoerp-infrastructure:ddm-3.0-RELEASE with classifier  to /dev-c-yy-gz-aipoerp-infrastructure/target/classes
2025-07-14 15:37:27,163 [Worker-58: Building Workspace] INFO  o.e.m.c.i.p.WorkspaceClassifierResolverManager - Resolving P/dev-c-yy-gz-aipoerp-service: com.yonyou.ucf:c-yy-gz-aipoerp-service:ddm-3.0-RELEASE with classifier  to /dev-c-yy-gz-aipoerp-service/target/classes
2025-07-14 15:37:27,210 [Worker-58: Building Workspace] INFO  o.e.m.c.i.p.WorkspaceClassifierResolverManager - Resolving P/dev-c-yy-gz-aipoerp-api: com.yonyou.ucf:c-yy-gz-aipoerp-api:ddm-3.0-RELEASE with classifier  to /dev-c-yy-gz-aipoerp-api/target/classes
2025-07-14 15:37:27,233 [Worker-58: Building Workspace] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-07-14 15:37:27,234 [Worker-58: Building Workspace] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-07-14 15:37:27,238 [Worker-58: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-07-14 15:37:27,238 [Worker-58: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-07-14 15:37:27,238 [Worker-58: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-app\src\main\resources
2025-07-14 15:37:27,238 [Worker-58: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-app\src\main\resources
2025-07-14 15:37:27,241 [Worker-58: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-07-14 15:37:27,241 [Worker-58: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-07-14 15:37:27,241 [Worker-58: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-app\src\test\resources
2025-07-14 15:37:27,242 [Worker-58: Building Workspace] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-07-14 15:37:27,243 [Worker-58: Building Workspace] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-07-14 15:37:28,590 [Worker-58: Building Workspace] INFO  o.e.m.c.i.p.WorkspaceClassifierResolverManager - Resolving P/dev-c-yy-gz-aipoerp-api: com.yonyou.ucf:c-yy-gz-aipoerp-api:ddm-3.0-RELEASE with classifier  to /dev-c-yy-gz-aipoerp-api/target/classes
2025-07-14 15:37:28,593 [Worker-58: Building Workspace] INFO  o.e.m.c.i.p.WorkspaceClassifierResolverManager - Resolving P/dev-c-yy-gz-aipoerp-app: com.yonyou.ucf:c-yy-gz-aipoerp-app:ddm-3.0-RELEASE with classifier  to /dev-c-yy-gz-aipoerp-app/target/classes
2025-07-14 15:37:28,593 [Worker-58: Building Workspace] INFO  o.e.m.c.i.p.WorkspaceClassifierResolverManager - Resolving P/dev-c-yy-gz-aipoerp-infrastructure: com.yonyou.ucf:c-yy-gz-aipoerp-infrastructure:ddm-3.0-RELEASE with classifier  to /dev-c-yy-gz-aipoerp-infrastructure/target/classes
2025-07-14 15:37:28,689 [Worker-58: Building Workspace] INFO  o.e.m.c.i.p.WorkspaceClassifierResolverManager - Resolving P/dev-c-yy-gz-aipoerp-service: com.yonyou.ucf:c-yy-gz-aipoerp-service:ddm-3.0-RELEASE with classifier  to /dev-c-yy-gz-aipoerp-service/target/classes
2025-07-14 15:37:28,826 [Worker-58: Building Workspace] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-07-14 15:37:28,829 [Worker-58: Building Workspace] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-07-14 15:37:28,844 [Worker-58: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-07-14 15:37:28,844 [Worker-58: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-07-14 15:37:28,845 [Worker-58: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 13 resources
2025-07-14 15:37:28,884 [Worker-58: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 2 resources
2025-07-14 15:37:28,891 [Worker-58: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-07-14 15:37:28,891 [Worker-58: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-07-14 15:37:28,891 [Worker-58: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-bootstrap\src\test\resources
2025-07-14 15:37:28,903 [Worker-58: Building Workspace] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-07-14 15:37:28,905 [Worker-58: Building Workspace] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-07-14 16:06:55,542 [Worker-66: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-07-14 16:06:55,542 [Worker-66: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-07-14 16:06:55,544 [Worker-66: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-07-14 16:06:55,545 [Worker-66: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-07-14 16:06:55,547 [Worker-66: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-07-14 16:06:55,548 [Worker-66: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-07-14 16:06:55,548 [Worker-66: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-service\src\test\resources
2025-07-14 16:07:55,982 [Worker-66: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-07-14 16:07:55,982 [Worker-66: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-07-14 16:07:55,982 [Worker-66: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-07-14 16:07:55,982 [Worker-66: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-07-14 16:07:55,985 [Worker-66: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-07-14 16:07:55,985 [Worker-66: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-07-14 16:07:55,985 [Worker-66: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-service\src\test\resources
2025-07-14 16:09:27,889 [Worker-62: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-07-14 16:09:27,889 [Worker-62: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-07-14 16:09:27,889 [Worker-62: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-07-14 16:09:27,890 [Worker-62: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-07-14 16:09:27,892 [Worker-62: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-07-14 16:09:27,892 [Worker-62: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-07-14 16:09:27,893 [Worker-62: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-service\src\test\resources
2025-07-14 16:10:38,796 [Worker-62: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-07-14 16:10:38,796 [Worker-62: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-07-14 16:10:38,796 [Worker-62: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-07-14 16:10:38,796 [Worker-62: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-07-14 16:10:38,799 [Worker-62: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-07-14 16:10:38,799 [Worker-62: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-07-14 16:10:38,799 [Worker-62: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-service\src\test\resources
2025-07-14 16:10:46,034 [Worker-66: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-07-14 16:10:46,034 [Worker-66: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-07-14 16:10:46,034 [Worker-66: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-07-14 16:10:46,034 [Worker-66: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-07-14 16:10:46,036 [Worker-66: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-07-14 16:10:46,037 [Worker-66: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-07-14 16:10:46,037 [Worker-66: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-service\src\test\resources
2025-07-14 16:10:56,977 [Worker-57: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-07-14 16:10:56,977 [Worker-57: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-07-14 16:10:56,978 [Worker-57: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-07-14 16:10:56,978 [Worker-57: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-07-14 16:10:56,981 [Worker-57: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-07-14 16:10:56,981 [Worker-57: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-07-14 16:10:56,981 [Worker-57: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-service\src\test\resources
2025-07-14 16:11:45,709 [Worker-64: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-07-14 16:11:45,709 [Worker-64: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-07-14 16:11:45,709 [Worker-64: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-07-14 16:11:45,709 [Worker-64: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-07-14 16:11:45,712 [Worker-64: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-07-14 16:11:45,712 [Worker-64: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-07-14 16:11:45,712 [Worker-64: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-service\src\test\resources
2025-07-14 16:12:24,972 [Worker-62: Push to aipoerp refs/heads/prd - origin] INFO  o.a.s.c.i.DefaultIoServiceFactoryFactory - No detected/configured IoServiceFactoryFactory; using Nio2ServiceFactoryFactory
2025-07-15 08:55:24,973 [Worker-6: Loading available Gradle versions] INFO  o.e.b.c.i.u.g.PublishedGradleVersions - Gradle version information cache is up-to-date. Trying to read.
2025-07-15 09:12:40,133 [Worker-26: Building Workspace] INFO  o.e.m.c.i.e.AbstractTransferListenerAdapter - Downloading https://repo.yyrd.com/artifactory/yonyou-public/com/yonyou/iuap/iuap-2nd-party/8.0.0-SNAPSHOT/maven-metadata.xml
2025-07-15 09:12:40,140 [Worker-26: Building Workspace] INFO  o.e.m.c.i.e.AbstractTransferListenerAdapter - Downloaded https://repo.yyrd.com/artifactory/yonyou-public/com/yonyou/iuap/iuap-2nd-party/8.0.0-SNAPSHOT/maven-metadata.xml
2025-07-15 09:12:40,221 [Worker-26: Building Workspace] INFO  o.e.m.c.i.e.AbstractTransferListenerAdapter - Downloading https://repo.yyrd.com/artifactory/yonyou-public/com/yonyou/iuap/iuap-3rd-party/8.0.0-SNAPSHOT/maven-metadata.xml
2025-07-15 09:12:40,224 [Worker-26: Building Workspace] INFO  o.e.m.c.i.e.AbstractTransferListenerAdapter - Downloaded https://repo.yyrd.com/artifactory/yonyou-public/com/yonyou/iuap/iuap-3rd-party/8.0.0-SNAPSHOT/maven-metadata.xml
2025-07-15 09:12:40,412 [Worker-26: Building Workspace] INFO  o.e.m.c.i.e.AbstractTransferListenerAdapter - Downloading https://repo.yyrd.com/artifactory/yonyou-public/com/yonyou/iuap/iuap-2nd-party/8.1.0-SNAPSHOT/maven-metadata.xml
2025-07-15 09:12:40,416 [Worker-26: Building Workspace] INFO  o.e.m.c.i.e.AbstractTransferListenerAdapter - Downloaded https://repo.yyrd.com/artifactory/yonyou-public/com/yonyou/iuap/iuap-2nd-party/8.1.0-SNAPSHOT/maven-metadata.xml
2025-07-15 09:12:40,480 [Worker-26: Building Workspace] INFO  o.e.m.c.i.e.AbstractTransferListenerAdapter - Downloading https://repo.yyrd.com/artifactory/yonyou-public/com/yonyou/iuap/iuap-3rd-party/8.1.0-SNAPSHOT/maven-metadata.xml
2025-07-15 09:12:40,483 [Worker-26: Building Workspace] INFO  o.e.m.c.i.e.AbstractTransferListenerAdapter - Downloaded https://repo.yyrd.com/artifactory/yonyou-public/com/yonyou/iuap/iuap-3rd-party/8.1.0-SNAPSHOT/maven-metadata.xml
2025-07-15 09:12:41,457 [Worker-26: Building Workspace] INFO  o.e.m.c.i.p.WorkspaceClassifierResolverManager - Resolving P/dev-c-yy-gz-aipoerp-api: com.yonyou.ucf:c-yy-gz-aipoerp-api:ddm-3.0-RELEASE with classifier  to /dev-c-yy-gz-aipoerp-api/target/classes
2025-07-15 09:12:41,459 [Worker-26: Building Workspace] INFO  o.e.m.c.i.p.WorkspaceClassifierResolverManager - Resolving P/dev-c-yy-gz-aipoerp-app: com.yonyou.ucf:c-yy-gz-aipoerp-app:ddm-3.0-RELEASE with classifier  to /dev-c-yy-gz-aipoerp-app/target/classes
2025-07-15 09:12:41,459 [Worker-26: Building Workspace] INFO  o.e.m.c.i.p.WorkspaceClassifierResolverManager - Resolving P/dev-c-yy-gz-aipoerp-infrastructure: com.yonyou.ucf:c-yy-gz-aipoerp-infrastructure:ddm-3.0-RELEASE with classifier  to /dev-c-yy-gz-aipoerp-infrastructure/target/classes
2025-07-15 09:12:41,562 [Worker-26: Building Workspace] INFO  o.e.m.c.i.p.WorkspaceClassifierResolverManager - Resolving P/dev-c-yy-gz-aipoerp-service: com.yonyou.ucf:c-yy-gz-aipoerp-service:ddm-3.0-RELEASE with classifier  to /dev-c-yy-gz-aipoerp-service/target/classes
2025-07-15 09:12:41,873 [Worker-26: Building Workspace] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-07-15 09:12:41,883 [Worker-26: Building Workspace] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-07-15 09:12:42,279 [Worker-26: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-07-15 09:12:42,280 [Worker-26: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-07-15 09:12:42,293 [Worker-26: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 13 resources
2025-07-15 09:12:42,477 [Worker-26: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 2 resources
2025-07-15 09:12:42,482 [Worker-26: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-07-15 09:12:42,482 [Worker-26: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-07-15 09:12:42,482 [Worker-26: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-bootstrap\src\test\resources
2025-07-15 09:12:42,493 [Worker-26: Building Workspace] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-07-15 09:12:42,494 [Worker-26: Building Workspace] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-07-15 09:15:00,560 [Worker-48: Building Workspace] INFO  o.e.m.c.i.p.WorkspaceClassifierResolverManager - Resolving P/dev-c-yy-gz-aipoerp-api: com.yonyou.ucf:c-yy-gz-aipoerp-api:ddm-3.0-RELEASE with classifier  to /dev-c-yy-gz-aipoerp-api/target/classes
2025-07-15 09:15:00,561 [Worker-48: Building Workspace] INFO  o.e.m.c.i.p.WorkspaceClassifierResolverManager - Resolving P/dev-c-yy-gz-aipoerp-infrastructure: com.yonyou.ucf:c-yy-gz-aipoerp-infrastructure:ddm-3.0-RELEASE with classifier  to /dev-c-yy-gz-aipoerp-infrastructure/target/classes
2025-07-15 09:15:00,586 [Worker-48: Building Workspace] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-07-15 09:15:00,587 [Worker-48: Building Workspace] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-07-15 09:15:00,608 [Worker-48: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-07-15 09:15:00,608 [Worker-48: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-07-15 09:15:00,608 [Worker-48: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-07-15 09:15:00,609 [Worker-48: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Copying 0 resource
2025-07-15 09:15:00,611 [Worker-48: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered resources.
2025-07-15 09:15:00,611 [Worker-48: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - Using 'UTF-8' encoding to copy filtered properties files.
2025-07-15 09:15:00,611 [Worker-48: Building Workspace] INFO  o.e.m.c.i.embedder.EclipseLogger - skip non existing resourceDirectory D:\HOMEWORK\yonyou\project\YUN2SHANG4GUI4ZHOU1\workspace\aipoerp\c-yy-gz-aipoerp-be\dev-c-yy-gz-aipoerp-service\src\test\resources
2025-07-15 09:15:00,613 [Worker-48: Building Workspace] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mcompile[0;1;33m ([0;1;33mdefault-compile[0;1;33m)[0;1;33m'[m
2025-07-15 09:15:00,614 [Worker-48: Building Workspace] WARN  o.a.m.l.i.DefaultMojoExecutionConfigurator - [1;33mParameter '[0;1;33mparameters[0;1;33m' is unknown for plugin '[0;1;33mmaven-compiler-plugin[0;1;33m:[0;1;33m3.2[0;1;33m:[0;1;33mtestCompile[0;1;33m ([0;1;33mdefault-testCompile[0;1;33m)[0;1;33m'[m
2025-07-15 09:15:30,704 [Worker-48: Push to aipoerp refs/heads/prd - origin] INFO  o.a.s.c.i.DefaultIoServiceFactoryFactory - No detected/configured IoServiceFactoryFactory; using Nio2ServiceFactoryFactory
