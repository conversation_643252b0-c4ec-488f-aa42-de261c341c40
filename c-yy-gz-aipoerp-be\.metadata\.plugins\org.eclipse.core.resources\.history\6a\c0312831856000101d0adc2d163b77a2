package com.yonyou.ucf.mdf.iris.plugin;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.apache.commons.lang3.StringUtils;
import org.imeta.orm.schema.QueryCondition;
import org.imeta.orm.schema.QueryConditionGroup;
import org.imeta.orm.schema.QueryField;
import org.imeta.orm.schema.QuerySchema;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Sets;
import com.yonyou.ucf.mdf.iris.enums.BusinessBillType;
import com.yonyou.ucf.mdf.iris.enums.SourceBusinessSystem;
import com.yonyou.ucf.mdf.iris.service.BillFileService;
import com.yonyou.ucf.mdf.iris.util.BankHttpRequest;
import com.yonyou.ypd.bill.infrastructure.service.api.IBillQueryRepository;
import com.yonyoucloud.ctm.stwb.plugin.dto.payindex.PayIndexDto;
import com.yonyoucloud.ctm.stwb.plugin.plugin.rpc.payindex.IPayIndexReqeustPlugin;

import cn.hutool.core.collection.CollUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2025/4/10 15:50
 * @DESCRIPTION 类描述
 */

/**
 * 这里的beanname与IRIS接口注册的beanId相同
 */
@Service("payIndexRequestPluginImpl")
@Slf4j
public class PayIndexRequestPluginImpl implements IPayIndexReqeustPlugin {
	@Autowired
	private BillFileService billFileService;

	@Autowired
	private IBillQueryRepository billQueryRepository;

	@Autowired
	private BankHttpRequest bankHttpRequest;

	@SuppressWarnings("unchecked")
	@Override
	public Map<String, Object> processPayIndexRequest(PayIndexDto payIndex, String tradeCode,
			Map<String, Object> request) {
		log.error("IRIS银企联结算客开结算测试PayIndexDto=========={}", JSONObject.toJSONString(payIndex));
		log.error("IRIS银企联结算客开结算测试tradeCode=========={}", tradeCode);
		log.error("IRIS银企联结算客开结算测试request=========={}", request);
		// 单笔支付
		if (!"11T10".equals(tradeCode)) {
			return null;
		}

		String body = JSONObject.toJSONString(request.get("request_body"));
		Map<String, Object> request_body = JSONObject.toJavaObject(JSONObject.parseObject(body), Map.class);
		request_body.put("pre_time", "093000"); // 默认加一个字段，支付信息加一个事件，9点30
		request.put("request_body", request_body);

		String private_extend = JSONObject.toJSONString(request_body.get("private_extend"));
		JSONObject privateExtend = new JSONObject();
		if (StringUtils.isNotBlank(private_extend)) {
			privateExtend = JSONObject.parseObject(private_extend);
		}
		privateExtend.put("extend0", "否");
		request_body.put("private_extend", privateExtend);

		Long settlebenchbid = payIndex.getSettlebenchbid();

		Integer businessBillType = payIndex.getBusinessbilltype();
		if (!BusinessBillType.getAllBusinessBillType().contains(businessBillType)) {
			log.error("1不需要上传附件的单据，返回给单笔支付结构的信息========{}", request);
			return request;
		}

		BusinessBillType businessBillTypeEnum = BusinessBillType.getBusinessBillType(businessBillType);
		if (businessBillTypeEnum == null) {
			log.error("2不需要上传附件的单据，返回给单笔支付结构的信息========{}", request);
			return request;
		}
		// 来源业务系统
		String banckup7 = payIndex.getBanckup7();
		SourceBusinessSystem sourceBusinessSystem = SourceBusinessSystem.getSourceBusinessSystem(banckup7);
		if (sourceBusinessSystem == null) {
			log.error("来源业务系统为空，返回给单笔支付结构的信息========{}", request);
			return request;
		}
		log.error("{}类型的单据进入单笔支付实现==================", sourceBusinessSystem.getDescription());
		// 实体URI/fullName
		String billUri = businessBillTypeEnum.getBillUri();
		// 扩展字段
		String customFiled = businessBillTypeEnum.getCustomFiled();
		String domain = sourceBusinessSystem.getDomain();
		// 来源单据编码
		String bizBillNo = payIndex.getBizbillno();
		// 查询来源单据信息
		Map<String, Object> businessBillMap = findBusinessBill(billUri, customFiled, domain, bizBillNo);
		if (CollUtil.isEmpty(businessBillMap)) {
			log.error("没有查询到需要上传的附件，返回给单笔支付结构的信息========{}", request);
			return request;
		}

		log.error("获取到的来源单据信息为========{}", businessBillMap);
		String businessBillId = toString(businessBillMap.get("id"));
		String billType = businessBillTypeEnum.getBillType();
		List<Map<String, Object>> fileBase64Maps = billFileService.getFileBase64Maps(businessBillId, billType,
				sourceBusinessSystem);
		if (CollUtil.isNotEmpty(fileBase64Maps)) {

			// 有附件传是
			privateExtend.put("extend0", "是");

			List<Map<String, String>> fileInfo = new ArrayList<>();
			for (Map<String, Object> fileBase64Map : fileBase64Maps) {
				if (CollUtil.isEmpty(fileBase64Map))
					continue;
				fileBase64Map.put("acct_no", payIndex.getPaybankno());
				fileBase64Map.put("acct_name", payIndex.getPaybankname());
				String result = bankHttpRequest.doPost(fileBase64Map, "11SC01");
				if (StringUtils.isNotBlank(result)) {
					fileInfo.add(JSONObject.toJavaObject(JSONObject.parseObject(result), Map.class));
				}
			}
			log.error("来源单据=={}上传的文件信息========{}", bizBillNo, fileInfo);
			request_body.put("file_info", fileInfo);
			log.error("添加附件后，返回给单笔支付结构的信息========{}", request);

			return request;
		}
		return request;
	}

	private Map<String, Object> findBusinessBill(String billUri, String customFiled, String domain, String bizBillNo) {
		// 这里查询如果是大额支付，则需要上传附件。否则如果是通用报销单、差旅费报销单。则还需要进一步判断费用项目关联的资金用途是否需要上传附件。如果是，则需要上传附件
		QuerySchema schema = QuerySchema.create().addSelect("id,code").addCondition(QueryConditionGroup
				.and(QueryCondition.name(customFiled + ".BX51").eq(true), QueryCondition.name("code").eq(bizBillNo)));
		List<Map<String, Object>> maps = billQueryRepository.queryMapBySchema(billUri, schema, domain);
		if (CollUtil.isNotEmpty(maps)) {
			return maps.get(0);
		}
		log.error("单据类型：{}，单据编号：{}，不是大额支付，查询费用项目");

		if (BusinessBillType.GENERAL_EXPENSE_ACCOUNT.getBillUri().equals(billUri)) { // 通用报销单查
			schema = QuerySchema.create();
			schema.addSelect("id");
			// 关联交易类型
			schema.addSelect(new QueryField("bustype", "bustype", null, "bd.bill.TransType/id"));
			schema.addSelect("bustype.code"); // 查交易类型编码

			// 指定子表（费用分摊）
			QuerySchema sonSchema = QuerySchema.create().name("expapportions").addSelect("*");

			schema.addCompositionSchema(sonSchema);

			schema.addCondition(QueryConditionGroup.and(QueryCondition.name("code").eq(bizBillNo)));

			maps = billQueryRepository.queryMapBySchema(billUri, schema, domain);
			if (CollUtil.isNotEmpty(maps)) {
				Map<String, Object> map = maps.get(0);
				Object bustype_code = map.get("bustype_code");
				if (bustype_code != null && "BXD06".equals(bustype_code.toString())) {
					// 公务接待、商务招待费用报销，直接传附件，不需要判断费用项目
					log.error("通用报销单：公务接待、商务招待费用报销，直接传附件");
					return map;
				} else if (bustype_code != null && "BXD12".equals(bustype_code.toString())) {
					// 对公付款单（重付），不需要传附件，所以直接返回null
					log.error("通用报销单：对公付款（重复），无需上传附件");
					return null;
				} else {
					// 其他情况需要判断费用项目关联的资金用途是否需要传附件
					Object expapportions = map.get("expapportions");
					if (expapportions == null) {
						log.error("通用报销单：{}不存在费用分摊，无需上传附件", bustype_code);
						return null;
					}
					Set<String> pk_busimemos = Sets.newHashSet();
					JSONArray expapportionJSONArray = JSONArray.parseArray(JSONObject.toJSONString(expapportions));
					for (int i = 0; i < expapportionJSONArray.size(); i++) {
						JSONObject expapportion = expapportionJSONArray.getJSONObject(i);
						String pk_busimemo = expapportion.getString("pk_busimemo");
						if (StringUtils.isNotBlank(pk_busimemo)) {
							pk_busimemos.add(pk_busimemo);
						}
					}
					if (pk_busimemos.isEmpty()) {
						log.error("通用报销单：{}费用分摊费用项目为空，无需上传附件", bustype_code);
						return null;
					}

					List<Map<String, Object>> expenseItems = queryBusimemos(pk_busimemos);

					if (CollUtil.isEmpty(expenseItems)) {
						log.error("通用报销单：{}按费用项目查询需要上传附件的费用项目为空，无需上传附件", bustype_code);
						return null;
					}
					return maps.get(0);
				}
			}
		} else if (BusinessBillType.TRAVEL_EXPENSE_REIMBURSEMENT_FORM.getBillUri().equals(billUri)) {
			// 差旅费报销单
			schema = QuerySchema.create();
			schema.addSelect("id");
			// 关联交易类型
			schema.addSelect(new QueryField("bustype", "bustype", null, "bd.bill.TransType/id"));
			schema.addSelect("bustype.code"); // 查交易类型编码

			// 指定子表（费用分摊）
			QuerySchema sonSchema = QuerySchema.create().name("expapportions").addSelect("*");

			schema.addCompositionSchema(sonSchema);

			schema.addCondition(QueryConditionGroup.and(QueryCondition.name("code").eq(bizBillNo)));

			maps = billQueryRepository.queryMapBySchema(billUri, schema, domain);

			if (CollUtil.isNotEmpty(maps)) {
				Map<String, Object> map = maps.get(0);
				Object expapportions = map.get("expapportions");
				if (expapportions == null) {
					log.error("差旅报销单：费用分摊为空，无需上传附件");
					return null;
				}
				Set<String> pk_busimemos = Sets.newHashSet();
				JSONArray expapportionJSONArray = JSONArray.parseArray(JSONObject.toJSONString(expapportions));
				for (int i = 0; i < expapportionJSONArray.size(); i++) {
					JSONObject expapportion = expapportionJSONArray.getJSONObject(i);
					String pk_busimemo = expapportion.getString("pk_busimemo");
					if (StringUtils.isNotBlank(pk_busimemo)) {
						pk_busimemos.add(pk_busimemo);
					}
				}
				if (pk_busimemos.isEmpty()) {
					log.error("差旅报销单：费用分摊费用项目为空，无需上传附件");
					return null;
				}

				List<Map<String, Object>> expenseItems = queryBusimemos(pk_busimemos);

				if (CollUtil.isEmpty(expenseItems)) {
					log.error("差旅报销单：{}按费用项目查询需要上传附件的费用项目为空，无需上传附件");
					return null;
				}
				return maps.get(0);

			}

		}

		log.error("单据类型：{}排除了所有需要上传附件的情况，无需上传附件", billUri);

		return null;
	}

	/**
	 * 查询费用项目
	 * 
	 * @param pk_busimemos
	 * @return
	 */
	@SuppressWarnings("unchecked")
	private List<Map<String, Object>> queryBusimemos(Set<String> pk_busimemos) {
		// 根据费用项目去查询关联的资金用途是否需要上传附件（因为资金用途是分配到费用项目上的特征项，所有使用sql去查还不好查）
		QuerySchema schemaExpenseItem = QuerySchema.create();
		schemaExpenseItem.addSelect("*");
		schemaExpenseItem.addSelect(
				new QueryField("character.FUND_USAGE", "fundUsage", null, "unitfyEnum.KKDOC.FundUsageDoc/id"));
		schemaExpenseItem.addSelect("character.FUND_USAGE");
		schemaExpenseItem.addSelect("character.FUND_USAGE.code");
		schemaExpenseItem.addSelect("character.FUND_USAGE.name");
		schemaExpenseItem.addSelect("character.FUND_USAGE.hasAttachment as hasAttachment");

		schemaExpenseItem.addCondition(QueryConditionGroup.and(QueryCondition.name("id").in(pk_busimemos),
				QueryCondition.name("character.FUND_USAGE.hasAttachment").eq("1")));

		List<Map<String, Object>> expenseItems = billQueryRepository.queryMapBySchema("bd.expenseitem.ExpenseItem",
				schemaExpenseItem, "c-yy-gz-aipoerp");
		return expenseItems;
	}

	private String toString(Object obj) {
		if (obj == null) {
			return null;
		}
		return obj.toString();
	}
}
