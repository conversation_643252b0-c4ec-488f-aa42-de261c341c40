package com.yonyou.ucf.mdf.rbsm.service.impl;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.imeta.orm.schema.QueryCondition;
import org.imeta.orm.schema.QueryConditionGroup;
import org.imeta.orm.schema.QueryJoin;
import org.imeta.orm.schema.QuerySchema;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import com.yonyou.aipierp.enums.ActionEnum;
import com.yonyou.ucf.mdf.rbsm.constants.PayTypeEnum;
import com.yonyou.ucf.mdf.rbsm.model.CommonExpenseBillVO;
import com.yonyou.ucf.mdf.rbsm.model.CommonExpenseSaveParam;
import com.yonyou.ucf.mdf.rbsm.model.CurUserInfo;
import com.yonyou.ucf.mdf.rbsm.model.Expapportion;
import com.yonyou.ucf.mdf.rbsm.model.ExpenseItem;
import com.yonyou.ucf.mdf.rbsm.model.Expensebillb;
import com.yonyou.ucf.mdf.rbsm.model.Expsettleinfo;
import com.yonyou.ucf.mdf.rbsm.model.FieldRef;
import com.yonyou.ucf.mdf.rbsm.model.LaborGenerateParam;
import com.yonyou.ucf.mdf.rbsm.model.StringListProcessor;
import com.yonyou.ucf.mdf.rbsm.model.VendorBank;
import com.yonyou.ucf.mdf.rbsm.model.VendorInfo;
import com.yonyou.ucf.mdf.rbsm.model.WaData;
import com.yonyou.ucf.mdf.rbsm.model.WaDataQryParam;
import com.yonyou.ucf.mdf.rbsm.model.WaPayRecord;
import com.yonyou.ucf.mdf.rbsm.model.WaPayRecordDetail;
import com.yonyou.ucf.mdf.rbsm.model.WaPayfile;
import com.yonyou.ucf.mdf.rbsm.service.itf.ICommonExpenseQryService;
import com.yonyou.ucf.mdf.rbsm.service.itf.ICommonExpenseService;
import com.yonyou.ucf.mdf.rbsm.service.itf.IExpenseItemService;
import com.yonyou.ucf.mdf.rbsm.service.itf.IFieldRefService;
import com.yonyou.ucf.mdf.rbsm.service.itf.ILaborPayBillGenerateService;
import com.yonyou.ucf.mdf.rbsm.service.itf.IPayOrgConfigQryService;
import com.yonyou.ucf.mdf.rbsm.service.itf.IVendorQryervice;
import com.yonyou.ucf.mdf.rbsm.service.itf.IWaDataQueryService;
import com.yonyou.ucf.mdf.rbsm.service.itf.IWaPayRecordQryService;
import com.yonyou.ucf.mdf.rbsm.utils.AppContext;
import com.yonyou.ucf.mdf.rbsm.utils.JSONUtil;
import com.yonyou.workbench.util.Lists;
import com.yonyou.ypd.bill.basic.entity.IBillDO;
import com.yonyou.ypd.bill.infrastructure.service.api.IBillCommonRepository;
import com.yonyou.ypd.bill.infrastructure.service.api.IBillRepository;

import cn.hutool.core.util.StrUtil;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 *
 *         2025年3月10日
 */
@Slf4j
@Service
public class LaborPayBillGenerateServiceImpl implements ILaborPayBillGenerateService {

	@Autowired
	AppContext appContext;

	@Autowired
	private IExpenseItemService expenseItemService;

	@Autowired
	private IFieldRefService fieldRefService;

	@Autowired
	IVendorQryervice vendorQryervice;

	@Autowired
	private IBillCommonRepository billCommonRepository;

	@Autowired
	private IWaDataQueryService waDataQueryService;

	@Autowired
	private ICommonExpenseService commonExpenseService;
	@Autowired
	private IWaPayRecordQryService waPayRecordQryService;
	@Autowired
	private ICommonExpenseQryService commonExpenseQryService;
	@Autowired
	private IBillRepository billRepository;
	@Autowired
	private IPayOrgConfigQryService payOrgConfigQryService;

	@SneakyThrows
	@Override
	public void generateLabor(LaborGenerateParam param) {
		List<String> periods = getPeriods(param);

		List<? extends IBillDO> billList = queryWaPayRecord(periods);
		if (!billList.isEmpty()) {
			List<Object> expenseIds = billList.stream().map(v -> v.getAttrValue("expenseId"))
					.collect(Collectors.toList());
			List<Map<String, Object>> bills = queryCommonExpenseByIds(expenseIds);
			if (!bills.isEmpty()) {
				StringBuilder message = new StringBuilder();
				message.append("【");
				message.append(param.getBelongYear());
				message.append("年第");
				message.append(param.getQuarter());
				message.append("季度】");
				message.append("已经生成了工会会费支付单: \r\n");
				for (Map<String, Object> bill : bills) {
					message.append("【单号：");
					message.append(bill.get("code") + "，");
					message.append("创建人：" + bill.get("creator") + "，");
					message.append("单据日期：" + bill.get("vouchdate") + "】\r\n");
				}
				log.error("查询出报销单：{}", JSONUtil.toJson(bills));
				throw new RuntimeException(message.toString());
			}
		}

		// 根据缴交期间查询发薪方案编码
		List<String> waSchemaCodes = queryWaSchemaCode(periods);

		List<WaData> waDatas = Lists.newArrayList();
		List<WaPayfile> waPayfiles = Lists.newArrayList(); // 所有薪资发放单
		List<JSONObject> waDataDetails = Lists.newArrayList(); // 所有发薪明细
		for (String waSchemaCode : waSchemaCodes) {
			WaDataQryParam waDataQryParam = new WaDataQryParam();
			waDataQryParam.setSchemeCode(waSchemaCode);
			for (String period : periods) {

				waDataQryParam.setPayPeriod(period);

				WaData waData = null;
				try {
					waData = waDataQueryService.queryWaDataByParam(waDataQryParam);
				} catch (Exception e) {
					log.error("调用薪资薪资发放单明显查询接口报错：{}", e.getMessage(), e);
					continue;
				}

				waDatas.add(waData);
				waPayfiles.addAll(waData.getWaPayfileVOList());
				for (List<JSONObject> details : waData.getWaPayfileDetailList().values()) {
					waDataDetails.addAll(details);
				}
			}
		}

		if (waDatas.isEmpty()) {
			throw new RuntimeException("没有查询到可以生成的工会会费数据");
		}

		// 薪资发放单按id封装成map
		Map<String, WaPayfile> waPayfileMap = waPayfiles.stream().collect(Collectors.toMap(WaPayfile::getId, v -> v));

		Map<String, List<JSONObject>> waDataDetailGroup = waDataDetails.stream().collect(Collectors.groupingBy(v -> {
			String taxOrgId = v.getString("TAX_ORG_ID");
			String taxDeptId = v.getString("TAX_DEPT_ID");
			if (taxDeptId == null) {
				log.error("调试日志------------财务部门为空---------：{}", JSONUtil.toJson(v));
			}
			return taxOrgId + "@" + taxDeptId;
		})); // 按财务组织和财务部门分组

		Map<String, CommonExpenseBillVO> laborPayments = generateLaborPayment(waPayfileMap, waDataDetailGroup, param);

		// 生成薪资发放单推送费控记录
		Map<String, WaPayRecord> waPayRecords = generateWaPayRecord(waPayfileMap, waDataDetailGroup);

		CommonExpenseSaveParam saveParam = new CommonExpenseSaveParam();

		for (String key : laborPayments.keySet()) {
			CommonExpenseBillVO laborPayment = laborPayments.get(key);
			saveParam.setData(laborPayment);

			log.error("调用通用报销单保存接口，参数：{}", JSONUtil.toJson(laborPayment));
			CommonExpenseBillVO resultBill = commonExpenseService.saveCommonExpense(saveParam);

			if (StringUtils.isNotBlank(resultBill.getId())) {
				WaPayRecord record = waPayRecords.get(key);
				record.setExpenseId(resultBill.getId());
				record.set_status(ActionEnum.INSERT.getValueInt());
				List<IBillDO> billDOs = Lists.newArrayList(record);
				billCommonRepository.commonSaveBill(billDOs, "WaPayRecord");
			} else {
				log.error("调用通用报销单保存接口返回id为空，参数：{}", JSONUtil.toJson(saveParam));
				throw new RuntimeException("调用通用报销单保存接口返回id为空");
			}

		}

	}

	/**
	 * 根据发薪期间查询发薪方案编码
	 * 
	 * @param periods
	 * @return
	 */
	private List<String> queryWaSchemaCode(List<String> periods) {
		String sql = creatQueryWaSchemaCodeSql(periods);
		List<String> codes = billRepository.queryForList(sql, null, new StringListProcessor());
		if (CollectionUtils.isEmpty(codes)) {
			// TODO 抛出异常或返回信息
			throw new RuntimeException("没有查询到薪资发放单");
		}
		return codes;
	}

	/**
	 * 查询发薪方案编码
	 * 
	 * @return
	 */
	private String creatQueryWaSchemaCodeSql(List<String> periods) {
		StringBuilder sb = new StringBuilder();
		sb.append("select                                            ");
		sb.append("	distinct										 ");
		sb.append("	was.code										 ");
		sb.append("from												 ");
		sb.append("	diwork_wa_mdd.wa_payfile wap					 ");
		sb.append("left join diwork_wa_mdd.wa_scheme_auth wasa on	 ");
		sb.append("	wap.pk_wa_scheme = wasa.id						 ");
		sb.append("left join diwork_wa_mdd.wa_scheme was on			 ");
		sb.append("	wasa.scheme_id = was.id							 ");
		sb.append("left join diwork_wa_mdd.wa_period_detail wapd on	 ");
		sb.append("	wap.pay_period = wapd.id						 ");
		sb.append("where											 ");
		sb.append(" was.enable = 1									 ");
		sb.append("	and wapd.year = '%s'							 ");
		sb.append("	and wapd.period in (%s)							 ");
		return String.format(sb.toString(), periods.get(0).substring(0, 4), periods.stream().map(s -> {
			String month = s.substring(5, 7);
			return StrUtil.wrap(month, "'");
		}).collect(Collectors.joining(",")));
	}

	/**
	 * 根据通用报销单id查询
	 * 
	 * @param expenseIds
	 * @return
	 */
	@SuppressWarnings("unchecked")
	private List<Map<String, Object>> queryCommonExpenseByIds(List<Object> expenseIds) {
		QuerySchema schema = QuerySchema.create();
		schema.addSelect("code,vouchdate,creator");
		schema.addCondition(QueryConditionGroup.and(QueryCondition.name("id").in(expenseIds)));
		return commonExpenseQryService.queryCommonExpense(schema);
	}

	/**
	 * 查询薪资发放单推送费控记录
	 * 
	 * @param periods
	 * @return
	 */
	@SuppressWarnings("unchecked")
	private List<? extends IBillDO> queryWaPayRecord(List<String> periods) {
		QuerySchema schema = QuerySchema.create();
		schema.addSelect("expenseId,WaPayRecordDetailList.pkPayfileDetail as pkPayfileDetail");
		schema.addJoin(new QueryJoin("WaPayRecordDetailList", null, "left"));
		schema.addCondition(
				QueryConditionGroup.and(QueryCondition.name("WaPayRecordDetailList.payPeriodName").in(periods),
						QueryCondition.name("sumType").eq("工会会费")));
		return waPayRecordQryService.queryWaPayRecord(schema);
	}

	/**
	 * 生成薪资发放单推送费控记录
	 * 
	 * @param waPayfileMap
	 * @param waDataDetailGroup
	 * @return
	 */
	private Map<String, WaPayRecord> generateWaPayRecord(Map<String, WaPayfile> waPayfileMap,
			Map<String, List<JSONObject>> waDataDetailGroup) {

		FieldRef fieldRef1 = fieldRefService.getFieldRefByKkField("LaborPayPersonal");
		if (fieldRef1 == null || StringUtils.isEmpty(fieldRef1.getRefField())) {
			throw new RuntimeException(
					"没有配置【个人-】工会会费字段编码【LaborPayPersonal】，请检查【数字化建模>字段映射>客开系统配置>字段映射】是否配置了【LaborPayPersonal】！");
		}
		String laborPayPersonalField = fieldRef1.getRefField();
		FieldRef fieldRef2 = fieldRefService.getFieldRefByKkField("LaborPayCompany");
		if (fieldRef2 == null || StringUtils.isEmpty(fieldRef2.getRefField())) {
			throw new RuntimeException(
					"没有配置【单位】工会会费字段编码【LaborPayCompany】，请检查【数字化建模>字段映射>客开系统配置>字段映射】是否配置了【LaborPayCompany】！");
		}
		String laborPayCompanyField = fieldRef2.getRefField();

		Map<String, WaPayRecord> waPayRecords = Maps.newHashMap();
		for (Entry<String, List<JSONObject>> entry : waDataDetailGroup.entrySet()) {
			List<JSONObject> values = entry.getValue();
			List<WaPayRecordDetail> details = values.stream().map(v -> {
				WaPayRecordDetail detail = new WaPayRecordDetail();
				String pkWaPayfile = v.getString("PK_WA_PAYFILE"); // 发放单id
				WaPayfile waPayfile = waPayfileMap.get(pkWaPayfile);
				detail.setCode(waPayfile.getCode());
				detail.setName(waPayfile.getName());
				detail.setScName(waPayfile.getSchemeName());
				detail.setPayPeriodName(waPayfile.getPayPeriodName());
				detail.setPayDate(waPayfile.getPayDate());
				detail.setPkPayfile(pkWaPayfile);
				detail.setPkPayfileDetail(v.getString("id"));
				detail.setStaffId(v.getString("staffId"));
				detail.setLaborPayPersonalAmount(v.getString(laborPayPersonalField));
				detail.setLaborPayCompanyAmount(v.getString(laborPayCompanyField));
				detail.set_status(ActionEnum.INSERT.getValueInt());
				return detail;
			}).collect(Collectors.toList());

			String[] keys = entry.getKey().split("@"); // 财务组织，财务部门
			WaPayRecord record = new WaPayRecord();
			record.setTaxOrg(keys[0]);
			record.setTaxDept(keys[1]);
			record.setSumType("工会会费");
			record.set_status(ActionEnum.INSERT.getValueInt());
			record.setWaPayRecordDetailList(details);

			waPayRecords.put(entry.getKey(), record);

		}
		return waPayRecords;
	}

	/**
	 * 生成工会会费支付单
	 * 
	 * @param waPayfileMap
	 * @param waDataDetailGroup
	 * @param param
	 * @return
	 */
	private Map<String, CommonExpenseBillVO> generateLaborPayment(Map<String, WaPayfile> waPayfileMap,
			Map<String, List<JSONObject>> waDataDetailGroup, LaborGenerateParam param) {

		CurUserInfo userInfo = appContext.getCurUserInfo();

		List<Map<String, Object>> payOrgCfgList = payOrgConfigQryService.queryByPayType(PayTypeEnum.LABOR);
		if (CollectionUtils.isEmpty(payOrgCfgList)) {
			throw new RuntimeException("没有配置工会费用支付相关财务信息，请检查【数字化建模>人力支付财务配置>人力支付财务配置>社保缴交机构财务配置】下配置");
		}
		Map<String, Map<String, Object>> payOrgCfgMap = payOrgCfgList.stream().collect(Collectors.toMap(v -> {
			Object payorg = v.get("cfinaceorg");
			if (payorg != null) {
				return payorg.toString();
			}
			return "";
		}, v -> v, (v1, v2) -> v2));

		List<String> vendorIds = payOrgCfgMap.values().stream().map(m -> {
			Object supplierId = m.get("supplierId");
			if (supplierId != null) {
				return supplierId.toString();
			}
			return null;
		}).filter(Objects::nonNull).distinct().collect(Collectors.toList());

		Map<String, VendorInfo> vendorMap = vendorQryervice.queryByIds(vendorIds);

		FieldRef fieldRef = fieldRefService.getFieldRefByKkField("laborBustype");
		if (fieldRef == null || StringUtils.isEmpty(fieldRef.getRefField())) {
			throw new RuntimeException(
					"没有配置工会会费支付单交易类型编码【laborBustype】，请检查【数字化建模>字段映射>客开系统配置>字段映射】是否配置了【laborBustype】！");
		}
		String laborBustype = fieldRef.getRefField();

		// 1、按财务组织、部门分组，再按期间和单位个人生成明细
		Map<String, List<Expensebillb>> expensebillbMap = generateExpensebillb(waDataDetailGroup, waPayfileMap,
				userInfo, payOrgCfgMap);
		if (expensebillbMap.isEmpty()) {
			// TODO 没有生成支付单明细数据，可能缴交金额都是0
			throw new RuntimeException("没有查询到可以生成的工会费缴交数据或者缴交数据合计金额为0");
		}

		// 2、根据支付单明细，生成费用分摊明细
		Map<String, List<Expapportion>> expapportionMap = generateExpapportion(expensebillbMap);

		// 3、根据支付单明细，生成结算信息
		Map<String, List<Expsettleinfo>> expsettleinfoMap = generateExpsettleinfo(expensebillbMap, vendorMap,
				payOrgCfgMap);

		// 4、创建工会会费支付单
		Map<String, CommonExpenseBillVO> laborPayments = Maps.newHashMap();
		for (String key : expensebillbMap.keySet()) {
			String[] keys = key.split("@"); // 财务组织、部门
			List<Expensebillb> expensebillbs = expensebillbMap.get(key);

			Map<String, Object> payOrgCfg = payOrgCfgMap.getOrDefault(keys[0], Collections.emptyMap());

			CommonExpenseBillVO laborPayment = new CommonExpenseBillVO();
			laborPayment.setDcostdate(userInfo.getBusDate());
			laborPayment.setVouchdate(userInfo.getBusDate());
			laborPayment.setVfinacedeptid(keys[1]);
			laborPayment.setCfinaceorg(keys[0]);
			laborPayment.setCaccountorg(keys[0]);
			laborPayment.setBustype(laborBustype); // 工会会费支付单
			laborPayment.setPk_handlepsn(userInfo.getStaffId());
			laborPayment.setVhandledeptid(userInfo.getDeptId());
			laborPayment.setChandleorg(userInfo.getOrgId());
			laborPayment.setDnatexchratedate(userInfo.getBusDate());
			BigDecimal nexpensemny = BigDecimal.ZERO; // 不含税总额
			BigDecimal nnatexpensemny = BigDecimal.ZERO; // 不含税总额-本币
			BigDecimal nsummny = BigDecimal.ZERO; // 价税总额
			BigDecimal nnatsummny = BigDecimal.ZERO; // 价税总额-本币
			for (Expensebillb expensebillb : expensebillbs) {
				if (StringUtils.isNotBlank(expensebillb.getNexpensemny())) {
					nexpensemny = nexpensemny.add(new BigDecimal(expensebillb.getNexpensemny()));
				}
				if (StringUtils.isNotBlank(expensebillb.getNnatexpensemny())) {
					nnatexpensemny = nnatexpensemny.add(new BigDecimal(expensebillb.getNnatexpensemny()));
				}
				if (StringUtils.isNotBlank(expensebillb.getNsummny())) {
					nsummny = nsummny.add(new BigDecimal(expensebillb.getNsummny()));
				}
				if (StringUtils.isNotBlank(expensebillb.getNnatsummny())) {
					nnatsummny = nnatsummny.add(new BigDecimal(expensebillb.getNnatsummny()));
				}
			}
			laborPayment.setNexpensemny(nexpensemny.setScale(2, RoundingMode.HALF_UP).toString());
			laborPayment.setNnatexpensemny(nnatexpensemny.setScale(2, RoundingMode.HALF_UP).toString());
			laborPayment.setNsummny(nsummny.setScale(2, RoundingMode.HALF_UP).toString());
			laborPayment.setNnatsummny(nnatsummny.setScale(2, RoundingMode.HALF_UP).toString());
			laborPayment.setNshouldpaymny(nsummny.setScale(2, RoundingMode.HALF_UP).toString());
			laborPayment.setNnatshouldpaymny(nnatsummny.setScale(2, RoundingMode.HALF_UP).toString());
			laborPayment.setPk_cusdoc(expensebillbs.get(0).getPk_cusdoc());
			laborPayment.setPk_cusdoc_code(expensebillbs.get(0).getPk_cusdoc_code());

			String explain = payOrgCfg.getOrDefault("explain", "").toString();
			if (StringUtils.isNotBlank(explain)) {
				explain = explain.replace("{period}", param.getBelongYear() + "年第" + param.getQuarter() + "季度");
				laborPayment.setVreason(explain);
			} else {
				laborPayment.setVreason(param.getBelongYear() + "年第" + param.getQuarter() + "季度工会费用支付");
			}

			// 设置用户id，接口查询会报错
//            socialSecurityPayment.setCreatorId(userInfo.getUserId());
//            socialSecurityPayment.setCreatorId("7cdf19ac-4902-497e-8690-45f3e9181972");
			laborPayment.setCreator_code(userInfo.getUserCode());

			// 子表数据
			laborPayment.setExpensebillbs(expensebillbs);
			laborPayment.setExpapportions(expapportionMap.get(key));
			laborPayment.setExpsettleinfos(expsettleinfoMap.get(key));
			if (CollectionUtils.isNotEmpty(expsettleinfoMap.get(key))) { // 社保结算单付款附言
				Expsettleinfo expsettleinfo = expsettleinfoMap.get(key).get(0);
				JSONObject expsettleinfoDcs = new JSONObject();
				expsettleinfoDcs.put("BX76", laborPayment.getVreason());
				expsettleinfo.setExpsettleinfoDcs(expsettleinfoDcs);
			}

			JSONObject expensebillDcs = new JSONObject();
			expensebillDcs.put("tz_quarter", param.getQuarter()); // 季度

			laborPayment.setExpensebillDcs(expensebillDcs);

			laborPayments.put(key, laborPayment);
		}
		return laborPayments;
	}

	/**
	 * 生成结算明细
	 * 
	 * @param expensebillbMap
	 * @param vendorInfo
	 * @return
	 */
	private Map<String, List<Expsettleinfo>> generateExpsettleinfo(Map<String, List<Expensebillb>> expensebillbMap,
			Map<String, VendorInfo> vendorMap, Map<String, Map<String, Object>> payOrgCfgMap) {
		Map<String, List<Expsettleinfo>> expsettleinfoMap = Maps.newHashMap();
		for (Entry<String, List<Expensebillb>> entry : expensebillbMap.entrySet()) {
			String[] keys = entry.getKey().split("@");// 财务组织、财务部门
			List<Expensebillb> values = entry.getValue();
			BigDecimal nsummny = BigDecimal.ZERO; // 结算合计付款金额
			for (Expensebillb expensebillb : values) {
				if (StringUtils.isNotBlank(expensebillb.getNsummny())) {
					nsummny = nsummny.add(new BigDecimal(expensebillb.getNsummny()));
				}
			}
			String nsummnyStr = nsummny.toString();
			Expsettleinfo expsettleinfo = new Expsettleinfo();

			Expensebillb expensebillb = values.get(0);

			Map<String, Object> payOrgCfg = payOrgCfgMap.getOrDefault(keys[0], Collections.emptyMap());

			VendorInfo vendorInfo = vendorMap.get(expensebillb.getPk_cusdoc());

			VendorBank vendorBank = getDefaultBank(vendorInfo);
			if (vendorBank != null) {
				expsettleinfo.setVbankaccount(vendorBank.getAccount()); // TODO 收款方账号
				expsettleinfo.setVbankaccname(
						vendorBank.getAccountname() != null ? vendorBank.getAccountname().getZh_CN() : ""); // TODO
				// 收款方户名
				expsettleinfo.setPk_bankdoc(vendorBank.getOpenaccountbank()); // TODO 收款方开户行
				expsettleinfo.setPk_banktype(vendorBank.getBank()); // TODO 收款银行类别
				expsettleinfo.setPk_cusdocbank(vendorBank.getId()); // TODO 供应商银行账户id
				expsettleinfo.setPk_cusdocbank_code(null);// TODO 供应商银行账户编码(付款类型igathertype为1时，id和编码必填一项)
				expsettleinfo.setAccttype(vendorBank.getAccountType());
			}
			expsettleinfo.setPk_cusdoc(vendorInfo.getId()); // TODO 供应商id(付款类型igathertype为1时，id和编码必填一项)
			expsettleinfo.setPk_cusdoc_code(vendorInfo.getCode()); // TODO 供应商编码(付款类型igathertype为1时，id和编码必填一项)
			expsettleinfo.setIgathertype("1"); // TODO 收款类型(0:个人;1:供应商;2:客户)
			expsettleinfo.setPk_balatype(payOrgCfg.getOrDefault("pkBalatype", "").toString()); // 结算方式id(结算方式编码和id必填一项)
			if (payOrgCfg.get("pkBalatype") == null) {
				expsettleinfo.setPk_balatype_code("system_0001"); // 如果没有配置默认结算方，则默认银行转账(结算方式编码和id必填一项)
			}
			expsettleinfo.setBalatypesrvattr("0"); // TODO 结算方式业务属性(0:银行业务;1:现金业务）
			expsettleinfo.setCenterpriseorg(expensebillb.getCfinaceorg());
			expsettleinfo.setPk_enterprisebankacct(payOrgCfg.getOrDefault("enterprisebank", "").toString());// 企业银行账户(支持id和code，结算方式为银行转账时必填)
			expsettleinfo.setVbankaccount_opp(payOrgCfg.getOrDefault("account", "").toString()); // 付款银行账号(结算方式为银行转账时必填)
			expsettleinfo.setAccttype_opp(payOrgCfg.getOrDefault("acctType", "").toString());// 付款账户类型（0:基本;1:一般;2:临时;3:专用）
			expsettleinfo.setVbankaccname_opp(payOrgCfg.getOrDefault("acctName", "").toString()); // 付款账户户名
			expsettleinfo.setPk_banktype_opp(payOrgCfg.getOrDefault("bank", "").toString()); // 付款银行类别(支持id和code，结算方式为银行转账时必填)
			expsettleinfo.setPk_bankdoc_opp(payOrgCfg.getOrDefault("bankNumber", "").toString()); // 付款开户行(支持id和code，结算方式为银行转账时必填)
			expsettleinfo.setPk_enterprisecashacct(null); // TODO 企业现金账户(支持id和code，结算方式为现金时必填)
			expsettleinfo.setDnatexchratedate(expensebillb.getDnatexchratedate());
			expsettleinfo.setNsummny(nsummnyStr);
			expsettleinfo.setNsettlesummny(nsummnyStr);
			expsettleinfo.setNnatsettlesummny(nsummnyStr);

			expsettleinfoMap.put(entry.getKey(), Arrays.asList(expsettleinfo));

		}
		return expsettleinfoMap;
	}

	/**
	 * 获取供应商默认银行账户，如果不存在默认银行账户，取第一条
	 *
	 * @param vendorInfo
	 * @return
	 */
	private VendorBank getDefaultBank(VendorInfo vendorInfo) {
		if (vendorInfo == null || CollectionUtils.isEmpty(vendorInfo.getVendorbanks())) {
			return null;
		}
		return vendorInfo.getVendorbanks().stream().filter(VendorBank::isDefaultbank).findFirst()
				.orElse(vendorInfo.getVendorbanks().get(0));
	}

	/**
	 * 生成分摊明细
	 * 
	 * @param expensebillbMap
	 * @return
	 */
	private Map<String, List<Expapportion>> generateExpapportion(Map<String, List<Expensebillb>> expensebillbMap) {
		Map<String, List<Expapportion>> expapportionMap = Maps.newHashMap();
		for (Entry<String, List<Expensebillb>> entry : expensebillbMap.entrySet()) {
			List<Expensebillb> values = entry.getValue();
			List<Expapportion> expapportionList = values.stream().map(v -> {
				Expapportion expapportion = new Expapportion();
				expapportion.setVfinacedeptid(v.getVfinacedeptid());
				expapportion.setCfinaceorg(v.getCfinaceorg());
				expapportion.setCaccountorg(v.getCaccountorg());
				expapportion.setPk_busimemo(v.getPk_busimemo());
				expapportion.setDnatexchratedate(v.getDnatexchratedate());
				expapportion.setNapportmny(v.getNsummny());
				expapportion.setNnatapportmny(v.getNnatsummny());
				expapportion.setNapportnotaxmny(v.getNexpensemny());
				expapportion.setNnatapportnotaxmny(v.getNnatexpensemny());
				return expapportion;
			}).collect(Collectors.toList());
			expapportionMap.put(entry.getKey(), expapportionList);
		}
		return expapportionMap;
	}

	/**
	 * 生成工会支付单明细数据
	 * 
	 * @param waDataDetailGroup
	 * @param waPayfileMap
	 * @param userInfo
	 * @param vendorInfo
	 * @return
	 */
	private Map<String, List<Expensebillb>> generateExpensebillb(Map<String, List<JSONObject>> waDataDetailGroup,
			Map<String, WaPayfile> waPayfileMap, CurUserInfo userInfo, Map<String, Map<String, Object>> payOrgCfgMap) {

		// 查询费用项目
		Map<String, ExpenseItem> expenseMap = expenseItemService.getAllExpenseItem();

		FieldRef fieldRef1 = fieldRefService.getFieldRefByKkField("LaborPayPersonal");
		if (fieldRef1 == null || StringUtils.isEmpty(fieldRef1.getRefField())) {
			throw new RuntimeException(
					"没有配置【个人-】工会会费字段编码【LaborPayPersonal】，请检查【数字化建模>字段映射>客开系统配置>字段映射】是否配置了【LaborPayPersonal】！");
		}
		String laborPayPersonalField = fieldRef1.getRefField().toUpperCase();
		FieldRef fieldRef2 = fieldRefService.getFieldRefByKkField("LaborPayCompany");
		if (fieldRef2 == null || StringUtils.isEmpty(fieldRef2.getRefField())) {
			throw new RuntimeException(
					"没有配置【单位】工会会费字段编码【LaborPayCompany】，请检查【数字化建模>字段映射>客开系统配置>字段映射】是否配置了【LaborPayCompany】！");
		}
		String laborPayCompanyField = fieldRef2.getRefField().toUpperCase();

		FieldRef fieldRef3 = fieldRefService.getFieldRefByKkField("工会会费（个人）");
		if (fieldRef3 == null || StringUtils.isEmpty(fieldRef3.getRefField())) {
			throw new RuntimeException("没有配置【工会会费（个人）】费用项目编码，请检查【数字化建模>字段映射>客开系统配置>字段映射】是否配置了【工会会费（个人）】！");
		}
		String laborPayPersonalFeeCode = fieldRef3.getRefField();

		FieldRef fieldRef4 = fieldRefService.getFieldRefByKkField("工会会费（单位）");
		if (fieldRef4 == null || StringUtils.isEmpty(fieldRef4.getRefField())) {
			throw new RuntimeException("没有配置【工会会费（单位）】费用项目编码，请检查【数字化建模>字段映射>客开系统配置>字段映射】是否配置了【工会会费（单位）】！");
		}
		String laborPayCompanyFeeCode = fieldRef4.getRefField();

		Map<String, List<Expensebillb>> expensebillbMap = Maps.newHashMap();

		for (Entry<String, List<JSONObject>> entry : waDataDetailGroup.entrySet()) {

			String[] keys = entry.getKey().split("@");// 财务组织、财务部门
			List<JSONObject> values = entry.getValue();
			Map<String, List<JSONObject>> valueGroup = values.stream().collect(Collectors.groupingBy(v -> {
				WaPayfile waPayfile = waPayfileMap.get(v.getString("PK_WA_PAYFILE"));
				return waPayfile.getPayPeriodName();
			})); // 按薪资期间分组

			Map<String, Object> payOrgCfg = payOrgCfgMap.getOrDefault(keys[0], Collections.emptyMap());

			List<Expensebillb> expensebillbs = Lists.newArrayList();
			for (Entry<String, List<JSONObject>> valueEntry : valueGroup.entrySet()) {

				List<JSONObject> valueList = valueEntry.getValue();

				BigDecimal personalAmount = BigDecimal.ZERO; // 个人工会费合计
				BigDecimal companyAmount = BigDecimal.ZERO; // 单位工会费合计

				for (JSONObject value : valueList) {
					BigDecimal laborPayPersonalAmount = value.getBigDecimal(laborPayPersonalField);
					BigDecimal laborPayCompanyAmount = value.getBigDecimal(laborPayCompanyField);
					if (laborPayPersonalAmount != null) {
						personalAmount = personalAmount.add(laborPayPersonalAmount);
					}
					if (laborPayCompanyAmount != null) {
						companyAmount = companyAmount.add(laborPayCompanyAmount);
					}
				}

				Expensebillb expensebillbP = new Expensebillb(); // 创建报销单明细（工会费个人）
				String personalAmountStr = personalAmount.setScale(2, RoundingMode.HALF_UP).toString();
				ExpenseItem expenseP = expenseMap.get(laborPayPersonalFeeCode);// 费用项目工会费个人
				if (expenseP == null) {
					// 费用项目为空
					throw new RuntimeException("没有查询到费用项目工会费个人");
				}
				// 费用项目信息
				expensebillbP.setPk_busimemo(expenseP.getId());
				expensebillbP.setDnatexchratedate(userInfo.getBusDate());
				expensebillbP.setPk_handlepsn(userInfo.getStaffId());
				expensebillbP.setVhandledeptid(userInfo.getDeptId());
				expensebillbP.setChandleorg(userInfo.getOrgId());
				expensebillbP.setCaccountorg(keys[0]);
				expensebillbP.setCfinaceorg(keys[0]);
				expensebillbP.setVfinacedeptid(keys[1]);
				expensebillbP.setPk_cusdoc(payOrgCfg.getOrDefault("supplierId", "").toString()); // TODO 供应商id
				expensebillbP.setPk_cusdoc_code(payOrgCfg.getOrDefault("supplierCode", "").toString()); // TODO 供应商编码
				expensebillbP.setNexpensemny(personalAmountStr);
				expensebillbP.setNnatexpensemny(personalAmountStr);
				expensebillbP.setNsummny(personalAmountStr);
				expensebillbP.setNnatsummny(personalAmountStr);
				expensebillbP.setNshouldpaymny(personalAmountStr);
				expensebillbP.setNnatshouldpaymny(personalAmountStr);
				expensebillbP.setNpaymentmny(personalAmountStr);
				expensebillbP.setNnatpaymentmny(personalAmountStr);

				// 使用特征和实施新增的特征项冲突，所以使用扩展字段来做
//				JSONObject expensebillBDcs = new JSONObject(); // 特征组
//				expensebillBDcs.put("tz_period", valueEntry.getKey()); // 期间
//				expensebillbP.setExpensebillBDcs(expensebillBDcs);

				expensebillbP.setExtend4(valueEntry.getKey());

				expensebillbs.add(expensebillbP);

				Expensebillb expensebillbC = new Expensebillb(); // 创建报销单明细（工会费单位）
				String companyAmountStr = companyAmount.setScale(2, RoundingMode.HALF_UP).toString();
				ExpenseItem expenseC = expenseMap.get(laborPayCompanyFeeCode);// 费用项目工会费单位
				if (expenseC == null) {
					// 费用项目为空
					throw new RuntimeException("没有查询到费用项目工会费单位");
				}
				// 费用项目信息
				expensebillbC.setPk_busimemo(expenseC.getId());
				expensebillbC.setDnatexchratedate(userInfo.getBusDate());
				expensebillbC.setPk_handlepsn(userInfo.getStaffId());
				expensebillbC.setVhandledeptid(userInfo.getDeptId());
				expensebillbC.setChandleorg(userInfo.getOrgId());
				expensebillbC.setCaccountorg(keys[0]);
				expensebillbC.setCfinaceorg(keys[0]);
				expensebillbC.setVfinacedeptid(keys[1]);
				expensebillbC.setPk_cusdoc(payOrgCfg.getOrDefault("supplierId", "").toString()); // TODO 供应商id
				expensebillbC.setPk_cusdoc_code(payOrgCfg.getOrDefault("supplierCode", "").toString()); // TODO 供应商编码
				expensebillbC.setNexpensemny(companyAmountStr);
				expensebillbC.setNnatexpensemny(companyAmountStr);
				expensebillbC.setNsummny(companyAmountStr);
				expensebillbC.setNnatsummny(companyAmountStr);
				expensebillbC.setNshouldpaymny(companyAmountStr);
				expensebillbC.setNnatshouldpaymny(companyAmountStr);
				expensebillbC.setNpaymentmny(companyAmountStr);
				expensebillbC.setNnatpaymentmny(companyAmountStr);

				// 使用特征和实施新增的特征项冲突，所以使用扩展字段来做
//				JSONObject expensebillBDcs = new JSONObject(); // 特征组
//				expensebillBDcs.put("tz_period", valueEntry.getKey()); // 期间
//				expensebillbC.setExpensebillBDcs(expensebillBDcs);

				expensebillbC.setExtend4(valueEntry.getKey());

				expensebillbs.add(expensebillbC);

			}

			if (!expensebillbs.isEmpty()) {
				expensebillbMap.put(entry.getKey(), expensebillbs);
			}
		}

		return expensebillbMap;
	}

	/**
	 * 根据参数生成需要查询的薪资期间
	 * 
	 * @param param
	 * @return
	 */
	private List<String> getPeriods(LaborGenerateParam param) {
		switch (param.getQuarter()) {
		case "1": // 一季度
			return Arrays.asList(param.getBelongYear() + "-01", param.getBelongYear() + "-02",
					param.getBelongYear() + "-03");
		case "2": // 二季度
			return Arrays.asList(param.getBelongYear() + "-04", param.getBelongYear() + "-05",
					param.getBelongYear() + "-06");
		case "3": // 三季度
			return Arrays.asList(param.getBelongYear() + "-07", param.getBelongYear() + "-08",
					param.getBelongYear() + "-09");
		case "4": // 四季度
			return Arrays.asList(param.getBelongYear() + "-10", param.getBelongYear() + "-11",
					param.getBelongYear() + "-12");

		default:
			break;
		}
		return Collections.emptyList();
	}

}
