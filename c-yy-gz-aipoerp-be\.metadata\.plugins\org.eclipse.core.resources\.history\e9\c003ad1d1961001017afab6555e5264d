package com.yonyou.ucf.mdf.iris.util;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSONObject;
import com.yonyou.ucf.mdf.iris.config.BankConfig;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/4/11 17:04
 * @DESCRIPTION 银企联http请求
 */
@Slf4j
@Component
public class BankHttpRequest {
    private volatile int a = 1;
    @Autowired
    private BankConfig bankConfig;

    @SneakyThrows
    public String doPost(Object reqData, String tranCode) {
        log.error("请求银企联后端接口reqData=========={}", reqData);
        log.error("请求银企联后端接口tranCode=========={}", tranCode);
        Map<String, Object> requestParam = new HashMap<>();
        requestParam.put("request_head", commonHead(tranCode));
        requestParam.put("request_body", reqData);
        String pramsStr = JSONObject.toJSONString(requestParam);
        String reqSignData = sign(pramsStr);
        requestParam.clear();
        requestParam.put("reqData", pramsStr);
        requestParam.put("reqSignData", reqSignData);
        log.error("请求银企联后端接口url=========={}", bankConfig.getUrl());
        String result = HttpUtil.createPost(bankConfig.getUrl())
                .header("Content-Type", "application/x-www-form-urlencoded;charset=utf8;")
                .form(requestParam)
                .execute().body();
        log.error("请求银企联后端接口返回结果=========={}", result);
        Map<String, String> formatMap = MapUtil.format(result);
        if (CollUtil.isEmpty(formatMap)) {
            return null;
        }
        String data = formatMap.get("data");
        if (StringUtils.isBlank(data)) {
            return null;
        }
        JSONObject jsonObject = JSONObject.parseObject(data);
        if (jsonObject == null) {
            return null;
        }
        JSONObject responseHead = jsonObject.getJSONObject("response_head");
        if (responseHead == null) {
            return null;
        }
        String serviceRespCode = responseHead.getString("service_resp_code");
        if (!"000000".equals(serviceRespCode)) {
            throw new RuntimeException(responseHead.getString("service_resp_desc"));
        }
        JSONObject responseBody = jsonObject.getJSONObject("response_body");
        if (responseBody == null) {
            return null;
        }
        return responseBody.getString("extend_data");
    }

    @SneakyThrows
    private String sign(String data) {
        return new CertificateUtil().sign(data, bankConfig.getPfxPath(), bankConfig.getPassword());
    }

    @SneakyThrows
    public boolean verify(String data) {
        String sign = sign(data);
        return new CertificateUtil().verify(data, sign, bankConfig.getCerPath());
    }

    private Map<String, Object> commonHead(String tranCode) {
        Map<String, Object> request_head = new HashMap<>();
        request_head.put("version", "1.0.0");
        request_head.put("request_seq_no", getRequestSeqNo(bankConfig.getCustNo()));
        request_head.put("cust_no", bankConfig.getCustNo());
        request_head.put("cust_chnl", bankConfig.getCustChnl());
        request_head.put("request_date", DateUtil.format(new Date(), DatePattern.PURE_DATE_PATTERN));
        request_head.put("request_time", DateUtil.format(new Date(), DatePattern.PURE_TIME_PATTERN));
        request_head.put("oper", "");
        request_head.put("oper_sign", "");
        request_head.put("tran_code", tranCode);
        request_head.put("auth_oper", "");
        request_head.put("extend_data", "");
        return request_head;
    }

    /**
     * 获取请求流水号
     */
    private String getRequestSeqNo(String customerNo) {
        if (StringUtils.isBlank(customerNo) || customerNo.length() != 6) {
            throw new RuntimeException("客户号为空或非6位");
        }
        return "R" + customerNo + "0000" +
                DateUtil.format(new Date(), DatePattern.PURE_DATETIME_MS_PATTERN) +
                getL4();

    }

    private String getL4() {
        if (a > 9999) {
            a = 1;
        }
        String i = "0000" + (a++);
        int len = i.length();
        return i.substring(len - 4, len);
    }
}
