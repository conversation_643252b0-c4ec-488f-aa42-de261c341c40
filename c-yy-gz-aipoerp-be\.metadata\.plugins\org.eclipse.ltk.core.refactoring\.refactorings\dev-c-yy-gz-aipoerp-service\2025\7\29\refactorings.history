<?xml version="1.0" encoding="UTF-8"?>
<session version="1.0">
<refactoring comment="Rename local variable &apos;expenseItems&apos; in &apos;com.yonyou.ucf.mdf.iris.plugin.PayIndexRequestPluginImpl.setFundUsage(...)&apos; to &apos;settleItems&apos;&#x0D;&#x0A;- Original project: &apos;dev-c-yy-gz-aipoerp-service&apos;&#x0D;&#x0A;- Original element: &apos;com.yonyou.ucf.mdf.iris.plugin.PayIndexRequestPluginImpl.setFundUsage(Long, JSONObject).expenseItems&apos;&#x0D;&#x0A;- Renamed element: &apos;expenseItems&apos;&#x0D;&#x0A;- Update references to refactored element" description="Rename local variable &apos;expenseItems&apos;" id="org.eclipse.jdt.ui.rename.local.variable" input="/src\/main\/java=/optional=/true=/=/maven.pomderived=/true=/&lt;com.yonyou.ucf.mdf.iris.plugin{PayIndexRequestPluginImpl.java[PayIndexRequestPluginImpl~setFundUsage~QLong;~QJSONObject;@expenseItems!6160!6314!6186!6197!QList\&lt;QMap\&lt;QString;QObject;&gt;;&gt;;!0!false" name="settleItems" references="true" stamp="1752480412582" version="1.0"/>
</session>