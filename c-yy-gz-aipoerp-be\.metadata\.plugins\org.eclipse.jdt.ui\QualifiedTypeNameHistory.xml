<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<qualifiedTypeNameHistroy>
    <fullyQualifiedTypeName name="org.springframework.stereotype.Service"/>
    <fullyQualifiedTypeName name="groovy.util.logging.Slf4j"/>
    <fullyQualifiedTypeName name="lombok.extern.slf4j.Slf4j"/>
    <fullyQualifiedTypeName name="com.yonyou.ucf.mdf.rbsm.utils.JSONUtil"/>
    <fullyQualifiedTypeName name="java.util.List"/>
    <fullyQualifiedTypeName name="java.text.SimpleDateFormat"/>
    <fullyQualifiedTypeName name="java.util.Map"/>
    <fullyQualifiedTypeName name="org.apache.commons.collections.CollectionUtils"/>
    <fullyQualifiedTypeName name="java.math.BigDecimal"/>
    <fullyQualifiedTypeName name="java.util.Date"/>
    <fullyQualifiedTypeName name="org.apache.commons.io.FilenameUtils"/>
    <fullyQualifiedTypeName name="java.time.Instant"/>
    <fullyQualifiedTypeName name="cn.hutool.json.JSONArray"/>
    <fullyQualifiedTypeName name="com.alibaba.fastjson.JSONArray"/>
    <fullyQualifiedTypeName name="java.util.Set"/>
    <fullyQualifiedTypeName name="com.google.common.collect.Sets"/>
    <fullyQualifiedTypeName name="org.apache.commons.lang3.StringUtils"/>
    <fullyQualifiedTypeName name="java.util.stream.Collectors"/>
    <fullyQualifiedTypeName name="java.lang.Object"/>
    <fullyQualifiedTypeName name="com.alibaba.fastjson.JSONObject"/>
</qualifiedTypeNameHistroy>
