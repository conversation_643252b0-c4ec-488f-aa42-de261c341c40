<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<launchConfiguration type="org.eclipse.jdt.launching.localJavaApplication">
    <listAttribute key="org.eclipse.debug.core.MAPPED_RESOURCE_PATHS">
        <listEntry value="/dev-c-yy-gz-aipoerp-service/src/test/java/com/yonyou/test/FileNameTest.java"/>
    </listAttribute>
    <listAttribute key="org.eclipse.debug.core.MAPPED_RESOURCE_TYPES">
        <listEntry value="1"/>
    </listAttribute>
    <stringAttribute key="org.eclipse.jdt.launching.CLASSPATH_PROVIDER" value="org.eclipse.m2e.launchconfig.classpathProvider"/>
    <stringAttribute key="org.eclipse.jdt.launching.MAIN_TYPE" value="com.yonyou.test.FileNameTest"/>
    <stringAttribute key="org.eclipse.jdt.launching.MODULE_NAME" value=""/>
    <stringAttribute key="org.eclipse.jdt.launching.PROJECT_ATTR" value="dev-c-yy-gz-aipoerp-service"/>
    <stringAttribute key="org.eclipse.jdt.launching.SOURCE_PATH_PROVIDER" value="org.eclipse.m2e.launchconfig.sourcepathProvider"/>
</launchConfiguration>
