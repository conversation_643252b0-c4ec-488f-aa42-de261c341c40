#spring\u57FA\u672C\u914D\u7F6E--start
spring.application.name=@engineCode@
spring.domain.name=DEV
#spring\u57FA\u672C\u914D\u7F6E--end

#\u5E94\u7528\u914D\u7F6E\u4FE1\u606F--start
spring.profiles.active=@profile@
access.key=@access.key@
access.secret=@access.secret@
#\u5E94\u7528\u914D\u7F6E\u4FE1\u606F--end

#yms\u914D\u7F6E--start
#\u9700\u8981\u914D\u7F6Eyms\u63A7\u5236\u53F0\u6CE8\u518C\u7684\u7F16\u7801
yms.application.name=@yms.application.name@
yms.redis.enable=true
yms.redis.code=@yms.redis.code@
yms.redis.code.pub=@yms.redis.code.pub@
yms.datasource.enable=true
yms.datasource.code=@yms.datasource.code@
yms.lock.redisClientCode=@yms.redis.code@
iuap.boot.redis.refs.defaultDef=@engineCode@_redis
#yms\u914D\u7F6E--end

#\u73AF\u5883\u53D8\u91CF\u914D\u7F6E--start
#\u662F\u5426\u542F\u7528\u53CB\u4E92\u901A
openYht=true
#\u7528\u6237id\uFF0C\u79DF\u6237id\u7B49\u662F\u5426\u90FD\u4EC5\u4F7F\u7528\u53CB\u4E92\u901Aid
onlyUseYhtTenant=true
#\u73AF\u5883\u53D8\u91CF\u914D\u7F6E--start

#UI\u5143\u6570\u636E\u914D\u7F6E--start
mdd.uimeta.prop.isMetaServer=true
#UI\u5143\u6570\u636E\u7EDF\u4E00\u5B58\u50A8
mdd.ui.tpl.domain=@uimetadata.domain@/mdf
#UI\u5143\u6570\u636E\u914D\u7F6E--end

#iris\u914D\u7F6E--start
MWCLIENT_ENABLE=true
mw_profiles_active=@iris.profile@
#app.version=dev_wuzq
#iris\u914D\u7F6E--end

#meta\u5143\u6570\u636E--start
metadata.api.url=@metadata.api.domain@
#\u7F13\u5B58\u901A\u77E5\u914D\u7F6E
pipelinecode=@metadata.pipelinecode@
# \u91CD\u65B0\u5F00\u542F\u5B9E\u4F53\u5143\u6570\u636E\u672C\u5730\u7F13\u5B58 600s
metadata.cache.timeout=72000
metadata.cache.maximumsize=500000
metadata.ui.url=@uimetadata.domain@
#meta\u5143\u6570\u636E--end

#\u6253\u5370\u670D\u52A1\u914D\u7F6E--start
#\u6253\u5370\u670D\u52A1\u9274\u6743\u6587\u4EF6: linux\u548Cmac\u76F4\u63A5\u521B\u5EFA\u6307\u5B9A\u6587\u4EF6; windows \u5728idea\u6216eclipse\u5B89\u88C5\u76EE\u5F55\u6240\u5728\u78C1\u76D8\u5206\u533A\u7684\u6839\u8DEF\u5F84\u521B\u5EFAapp\u76EE\u5F55
u8cprint.client.credential.path=printauth.properties
print.client.credential.path=printauth.properties
print.entrance.name=@print.entrance.name@
printBaseUrl=@printBaseUrl@
UAP.AUTH.ALG=HMAC
UAP.KDF.PRF=HmacSHA1
#\u6253\u5370\u670D\u52A1\u914D\u7F6E--end

#\u5BA1\u6279\u6D41\u914D\u7F6E  --start
bpmrest.appsource=@engineCode@
bpmrest.server=${domain.url}/iuap-apcom-workflow
#\u5BA1\u6279\u6D41\u914D\u7F6E  --end

#ES\u914D\u7F6E--begin
ucf.intellis.endpoint=@ucf.search.endpoint@
ucf.intellis.tenantId.default=@ucf.search.tenantId.default@
ucf.search.token.default=@ucf.search.token.default@
#ES\u914D\u7F6E--end

#yonscript--start
ublinker.env=pre
#\u5916\u90E8\u8C03\u8BD5\u5668\u670D\u52A1\u5730\u5740
J2V8_DEBUGER_HOST= @J2V8_DEBUGER_HOST@
#j2v8 debug server \u8D85\u65F6\u65F6\u95F4ms 10min
J2V8_DEBUGER_TIMEOUT = 600000
hpapaas-passport-be.host=@passport.domain@
#\u8C03\u8BD5\u53E5\u67C4\u8FC7\u671F\u65F6\u95F4\uFF08\u79D2\uFF09\u6BCF\u4E2A\u53E5\u67C4\u4E00\u5C0F\u65F6\u7684\u5360\u7528\u65F6\u95F4
debug_handler_expire=3600
#\u8C03\u8BD5\u53E5\u67C4\u6807\u8BB0\u4F7F\u7528\u7684redis
#redis_url=direct://@spring.redis.host@:@spring.redis.port@?poolSize=50&poolName=mypool&password=@spring.redis.password@
#yonscript--end


#\u5206\u5E03\u5F0F\u9501--start
zklock.url=@zk.host@:@zk.port@
zookeeper-address=@zk.host@:@zk.port@
#\u5206\u5E03\u5F0F\u9501--end

#\u4E1A\u52A1\u6D41--start
bizFlow.url=@bizFlow.url@
bizFlow.contextUrl=@bizFlow.contextUrl@
#\u4E1A\u52A1\u6D41--end

#\u5355\u636E\u7F16\u53F7--start
#\u8BBE\u7F6E\u662F\u5426\u542F\u7528\u6D41\u6C34\u53F7\u7F13\u5B58\uFF0C\u9ED8\u8BA4\u542F\u7528
billcode.cacheenable=true
#\u8868\u793A\u7F13\u5B58\u7684\u6700\u5927\u6570\u636E\u91CF\uFF0C\u9ED8\u8BA410
billcode.cachemax=10
#\u8868\u793A\u7F13\u5B58\u7684\u6700\u5C0F\u6570\u636E\u91CF\uFF0C\u9ED8\u8BA41
billcode.cachemin=1
#\u8BBE\u7F6E\u79DF\u6237\u6A21\u5F0F\uFF0C1\uFF1A\u53CB\u4E92\u901A 2\uFF1A\u8425\u9500\u4E91(\u9ED8\u8BA4)
billcode.tenantmode=1
#\u627E\u4E0D\u5230\u7F16\u7801\u89C4\u5219\u65F6\u9ED8\u8BA4\u81EA\u52A8\u7F16\u53F7
billcode.nobillnumber.showdefault=true
# \u7F16\u7801\u89C4\u5219\u5982\u679C\u6CA1\u6709\u9884\u5236\u5019\u9009\u5C5E\u6027\uFF0C\u53EF\u4EE5\u76F4\u63A5\u663E\u793AUI\u5143\u6570\u636E\u91CC\u7684\u5019\u9009\u5C5E\u6027
billcode.noprefabricate.showUImeta=true
#\u5355\u636E\u7F16\u53F7--end

#spring\u5176\u4ED6\u914D\u7F6E--start
# \u6587\u4EF6\u4E0A\u4F20\u5927\u5C0F\u9650\u5236
spring.servlet.multipart.max-file-size=20MB
spring.servlet.multipart.max-request-size=20MB
#\u56FD\u9645\u5316\u591A\u8BED\u914D\u7F6E
spring.messages.basename=i18n/messages
spring.messages.cache-duration=3600
spring.messages.encoding=UTF-8
#\u7F16\u7801
spring.http.encoding.force=true
spring.http.encoding.charset=utf-8
spring.http.encoding.enabled=true
server.tomcat.uri-encoding=UTF-8
spring.main.allow-bean-definition-overriding=true
# actuator\u7684\u8BBF\u95EE\u8DEF\u5F84
management.endpoints.web.base-path=/manage
# \u4E0A\u4E0B\u6587\u8DEF\u5F84 \u914D\u7F6E\u7684\u60C5\u51B5\u8BBF\u95EE\u9700\u8981\u7528 ip:port/mdf/manage/health
management.server.servlet.context-path=/mdf
#server \u5730\u5740 \u8BBE\u7F6E127.0.0.1\u540E \u4E0D\u5141\u8BB8\u8FDC\u7A0B\u7BA1\u7406\u8FDE\u63A5:
#management.server.address=127.0.0.1
# \u7BA1\u7406\u7684\u7AEF\u53E3\u8C03\u6574\u62101234  \u5982\u679C\u4E0D\u5E0C\u671B\u901A\u8FC7HTTP\u516C\u5F00\u7AEF\u70B9\uFF0C\u5219\u53EF\u4EE5\u5C06\u7BA1\u7406\u7AEF\u53E3\u8BBE\u7F6E\u4E3A-1
management.server.port=-1
#\u5173\u95ED\u9ED8\u8BA4\u542F\u7528\u6A21\u5F0F
management.endpoints.enabled-by-default = false
management.endpoint.info.enabled = true
management.endpoint.health.enabled = true
#http://ip:port/demo-prometheus/actuator/prometheus
management.endpoint.prometheus.enabled=true
management.endpoint.health.show-details=always
#\u66B4\u9732 \u7AEF\u70B9\u914D\u7F6E info,health,prometheus,custom
management.endpoints.web.exposure.include=info,health,prometheus,custom
#\u5173\u95ED\u9ED8\u8BA4\u914D\u7F6E\u7684db\u68C0\u67E5
management.health.db.enabled=false
#\u5173\u95ED\u9ED8\u8BA4\u914D\u7F6E\u7684redis\u68C0\u67E5
management.health.redis.enabled=false
# shutdown \u53EF\u4EE5\u5173\u95ED\u5236\u5B9A\u7684\u7AEF\u70B9
management.endpoint.shutdown.enabled=false
#groupby \u9002\u914D\u591A\u6570\u636E\u5E93\u7684\u5F3A\u7EA6\u675F\u914D\u7F6E
mdd.groupby.compatible=true
#\u5BFC\u5165service
import.invalid.handle.data.save=importSaveServiceImpl
reportGroup=@engineCode@
#\u591A\u8BED\u6570\u636E\u8FD4\u56DE\u5BF9\u8C61\u5F00\u5173
mdd.i18n.enable=true
runtime.server.url=${domain.url}/@engineCode@
A47.redis.ref=@yms.redis.code@
redis.mainIndex=1
domain.iuap-apcom-i18n=${domain.url}/iuap-apcom-i18n
# mdf\u5728\u534F\u540C\u5BF9\u5E94\u7684appcode
mdf.appcode=@engineCode@
#\u53C2\u7167\u6536\u85CF\u529F\u80FD\u5F00\u5173
ypd.open.refHot=true
mdd.cooperation.business.line=@engineCode@

#\u6309\u7167\u6B63\u5219\u6392\u9664\u4E0D\u9700\u8981\u6062\u590D\u4E0A\u4E0B\u6587\u8DEF\u5F84 \u65B0\u7684\u6B63\u5219\u9700\u8981\u5E26\u4E0A\u539F\u6765\u9ED8\u8BA4\u6B63\u5219
yms.session.ymsSessionWhite="^.*(/ping/?)$"
# \u6309\u7167\u540E\u7F00\u6392\u9664\u4E0D\u9700\u8981\u6062\u590D\u4E0A\u4E0B\u6587\u8DEF\u5F84 \u4EE5;\u5206\u5272
yms.session.whiteListSuffixs=".css;.js;.jpg;.png;.ttf;.ico;.html;.jsx;.gif"
# \u5305\u542B\u65B9\u5F0F\uFF1A\u4EE5;\u5206\u5272
yms.session.whiteListContain=/ping/;health;/extend/healthycheck;/sendphonemessage/send;/rest/;
#\u6309\u7167\u6B63\u5219\u6392\u9664
yms.session.greyListRegex="^.*(/ping/?)$"
#\u6309\u7167\u540E\u7F00\u6392\u9664 \u4EE5;\u5206\u5272
yms.session.greyListSuffixs=".css;.js;.jpg;.png;.ttf;.ico;.html;.jsx;.gif"
#\u5305\u542B\u65B9\u5F0F\uFF1A\u4EE5;\u5206\u5272
yms.session.greyListContain="/ping/;health"
#\u8D8A\u6743\u627E\u4E0D\u5230action\u65F6\u4F7F\u7528commoncommand
ypd.custom.defaultCheckCommonCommandAction=true
mdd.import.event.v2.switch=true
disconf.conf_server_host=@disconf.conf_server_host@
registry=@registry@
iris.serviceUrl.defaultZone=@iris.serviceUrl.defaultZone@

# \u8D44\u4EA7\u6570\u636E\u5BFC\u5165openAPI
app.gateway-address-url=${domain.url}/iuap-api-auth/open-auth/dataCenter/getGatewayAddress?tenantId=${app.tenantId}
app.open-api.appKey=${bip.open-api.appKey}
app.open-api.appSecret=${bip.open-api.appSecret}

# \u5B89\u5168\u8BA4\u8BC1\u7F51\u5173\u7B7E\u540D\u8BC1\u4E66
app.bank.pfx-path=/certs/C13112.pfx
#\u8BC1\u4E66\u53E3\u4EE4
app.bank.password=********
# \u94F6\u884CCER\u8BC1\u4E66\u914D\u7F6E(\u7528\u4E8E\u9A8C\u7B7E)
app.bank.cer-path=/certs/NJCARSA.cer
# \u94F6\u4F01\u8054\u8BF7\u6C42\u5730\u5740
app.bank.url=https://***********:9443/api-forward/access.jdo
app.bank.cust_no=C13112
app.bank.cust_chnl=0000