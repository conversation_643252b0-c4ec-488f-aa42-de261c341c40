package com.yonyou.ucf.mdf.rbsm.service.impl;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.yonyou.aipierp.enums.ActionEnum;
import com.yonyou.iuap.yms.param.SQLParameter;
import com.yonyou.ucf.mdf.rbsm.constants.ExpenseEnum;
import com.yonyou.ucf.mdf.rbsm.constants.PayTypeEnum;
import com.yonyou.ucf.mdf.rbsm.model.CommonExpenseBillVO;
import com.yonyou.ucf.mdf.rbsm.model.CommonExpenseSaveParam;
import com.yonyou.ucf.mdf.rbsm.model.CurUserInfo;
import com.yonyou.ucf.mdf.rbsm.model.Expapportion;
import com.yonyou.ucf.mdf.rbsm.model.ExpenseItem;
import com.yonyou.ucf.mdf.rbsm.model.Expensebillb;
import com.yonyou.ucf.mdf.rbsm.model.Expsettleinfo;
import com.yonyou.ucf.mdf.rbsm.model.FieldRef;
import com.yonyou.ucf.mdf.rbsm.model.GenerateSocialParam;
import com.yonyou.ucf.mdf.rbsm.model.InsurancePayDetail;
import com.yonyou.ucf.mdf.rbsm.model.SocialSecurityDetail;
import com.yonyou.ucf.mdf.rbsm.model.SocialSecurityRecord;
import com.yonyou.ucf.mdf.rbsm.model.StringListProcessor;
import com.yonyou.ucf.mdf.rbsm.model.VendorBank;
import com.yonyou.ucf.mdf.rbsm.model.VendorInfo;
import com.yonyou.ucf.mdf.rbsm.service.itf.ICommonExpenseService;
import com.yonyou.ucf.mdf.rbsm.service.itf.IExpenseItemService;
import com.yonyou.ucf.mdf.rbsm.service.itf.IExpensebillGenerateService;
import com.yonyou.ucf.mdf.rbsm.service.itf.IFieldRefService;
import com.yonyou.ucf.mdf.rbsm.service.itf.IPayOrgConfigQryService;
import com.yonyou.ucf.mdf.rbsm.service.itf.IPaymentInstitutionService;
import com.yonyou.ucf.mdf.rbsm.service.itf.IVendorQryervice;
import com.yonyou.ucf.mdf.rbsm.utils.AppContext;
import com.yonyou.ucf.mdf.rbsm.utils.JSONUtil;
import com.yonyou.ypd.bill.basic.entity.IBillDO;
import com.yonyou.ypd.bill.infrastructure.service.api.IBillCommonRepository;
import com.yonyou.ypd.bill.infrastructure.service.api.IBillRepository;

import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

/**
 * 报销单自动生成接口实现
 *
 * <AUTHOR>
 *
 *         2025年2月21日
 */
@Slf4j
@Service
public class ExpensebillGenerateServiceImpl implements IExpensebillGenerateService {

	@Autowired
	private IBillRepository billRepository;

	@Autowired
	AppContext appContext;

	@Autowired
	IVendorQryervice vendorQryervice;

	@Autowired
	private IBillCommonRepository billCommonRepository;

	@Autowired
	private IExpenseItemService expenseItemService;

	@Autowired
	private IFieldRefService fieldRefService;

	@Autowired
	private ICommonExpenseService commonExpenseService;

	@Autowired
	private IPayOrgConfigQryService payOrgConfigQryService;
	@Autowired
	private IPaymentInstitutionService paymentInstitutionService;

	/**
	 * 租户id
	 */
	@Value("${app.tenantId}")
	private String tenantId;

	@SneakyThrows
	@Override
	public void generateSocial(GenerateSocialParam gsparam) {
		// 查询可用于生成社保福利支付单的社保缴交记录
		List<InsurancePayDetail> insurancePayDetailList = getInsurancePayDetail(gsparam);

		PayTypeEnum payType = PayTypeEnum.SOCIAL_SECURITY;
		if ("公积金".equals(gsparam.getSocialType())) {
			payType = PayTypeEnum.FUND;
		}
		List<Map<String, Object>> payOrgCfgList = payOrgConfigQryService.queryByPayType(payType);
		if (CollectionUtils.isEmpty(payOrgCfgList)) {
			throw new RuntimeException(String.format("没有配置%s支付相关财务信息，请检查【数字化建模>人力支付财务配置>人力支付财务配置>社保缴交机构财务配置】下配置",
					gsparam.getSocialType()));
		}

		Map<String, Map<String, Object>> payOrgCfgMap = payOrgCfgList.stream().collect(Collectors.toMap(v -> {
			Object payorg = v.get("payorg");
			if (payorg != null) {
				return payorg.toString();
			}
			return "";
		}, v -> v, (v1, v2) -> v2));

		// 设置财务组织和财务部门
		Set<String> piNames = Sets.newHashSet();
		for (InsurancePayDetail insurancePayDetail : insurancePayDetailList) {
			Map<String, Object> payOrgCfg = payOrgCfgMap.get(insurancePayDetail.getPiId());
			if (payOrgCfg != null && payOrgCfg.get("cfinaceorg") != null && payOrgCfg.get("cfinacedept") != null) {
				insurancePayDetail.setTaxOrgId(payOrgCfg.get("cfinaceorg").toString());
				insurancePayDetail.setTaxDeptId(payOrgCfg.get("cfinacedept").toString());
			} else {
				piNames.add(insurancePayDetail.getPiName());
			}
		}

		if (!piNames.isEmpty()) {
			throw new RuntimeException(String.format("没有查询到以下缴交机构的财务组织和财务部门，请检查配置！\r\n【%s】",
					piNames.stream().collect(Collectors.joining(","))));
		}

		// 生成社保福利支付单
		Map<String, CommonExpenseBillVO> socialSecurityPayments = generateSecurityPayment(insurancePayDetailList,
				gsparam, payOrgCfgMap);

		// 生成社保缴交记录
		Map<String, SocialSecurityRecord> socialSecurityRecords = generateSocialSecurityRecord(insurancePayDetailList,
				gsparam);

		CommonExpenseSaveParam saveParam = new CommonExpenseSaveParam();
		for (String key : socialSecurityPayments.keySet()) {
			CommonExpenseBillVO socialSecurityPayment = socialSecurityPayments.get(key);

			saveParam.setData(socialSecurityPayment);

			CommonExpenseBillVO resultBill = commonExpenseService.saveCommonExpense(saveParam);

			if (StringUtils.isNotBlank(resultBill.getId())) {
				SocialSecurityRecord record = socialSecurityRecords.get(key);
				record.setExpenseId(resultBill.getId());
				record.set_status(ActionEnum.INSERT.getValueInt());
				List<IBillDO> billDOs = Lists.newArrayList(record);
				billCommonRepository.commonSaveBill(billDOs, "SocialSecurityRecord");
			} else {
				log.error("调用通用报销单保存接口返回id为空，参数：{}", JSONUtil.toJson(saveParam));
				throw new RuntimeException("调用通用报销单保存接口返回id为空");
			}

		}
	}

	/**
	 * 获取社保缴交明细数据
	 *
	 * @return
	 */
	private List<InsurancePayDetail> getInsurancePayDetail(GenerateSocialParam param) {
		String sql = createQueryInsurancePayDetailSql();
		SQLParameter parameter = new SQLParameter();
		parameter.addParam(param.getSocialType());
		parameter.addParam(tenantId);
		parameter.addParam(param.getPeriod());
		List<InsurancePayDetail> insurancePayDetailList = billRepository.queryForDTOList(sql, parameter,
				InsurancePayDetail.class);

		if ("公积金".equals(param.getSocialType())) {
			Map<String, Map<String, Object>> paymentInstitutionMap = paymentInstitutionService.queryAllForMap();
			insurancePayDetailList = insurancePayDetailList.stream().filter(v -> {
				Map<String, Object> paymentInstitution = paymentInstitutionMap.get(v.getPiId());
				if (paymentInstitution != null && paymentInstitution.get("piType") != null
						&& paymentInstitution.get("piType").toString().equals("0")) {
					return false;
				}
				return true;
			}).collect(Collectors.toList()); // 把外部缴交机构去掉
		}

		if (CollectionUtils.isEmpty(insurancePayDetailList)) {
			// 如果为空，查询一下是否已经生成了对应单据
			String hasSql = createQueryInsurancePayDetailAlreadySql();
			SQLParameter hasParameter = new SQLParameter();
			hasParameter.addParam(param.getSocialType());
			hasParameter.addParam(tenantId);
			hasParameter.addParam(param.getPeriod());
			List<Object> insuranceIdList = billRepository.queryForList(hasSql, hasParameter, new StringListProcessor());
			if (CollectionUtils.isEmpty(insuranceIdList)) {
				// TODO 抛出异常或返回信息
				throw new RuntimeException(String.format("社保期间【%s】没有查询到可以生成的社保缴交数据", param.getPeriod()));
			} else {
				List<? extends IBillDO> bills = commonExpenseService.queryCommonExpenseByIds(insuranceIdList);
				if (!bills.isEmpty()) {
					StringBuilder message = new StringBuilder();
					message.append("社保期间【");
					message.append(param.getPeriod());
					message.append("】");
					if ("社保".equals(param.getSocialType())) {
						message.append("已经生成了社保支付单: \r\n");
					} else {
						message.append("已经生成了公积金支付单: \r\n");
					}
					for (IBillDO bill : bills) {
						message.append("【单号：");
						message.append(bill.getAttrValue("code") + "，");
						message.append("创建人：" + bill.getCreator() + "，");
						message.append("单据日期：" + bill.getAttrValue("vouchdate") + "】\r\n");
					}
					log.error("查询出报销单：{}", JSONUtil.toJson(bills));
					throw new RuntimeException(message.toString());
				}
			}
		}
		return insurancePayDetailList;
	}

	/**
	 * @return
	 */
	private String createQueryInsurancePayDetailAlreadySql() {
		StringBuilder sb = new StringBuilder();
		sb.append(" select															");
		sb.append("     distinct 												  	");
		sb.append(" 	t.expense_id 												");
		sb.append(" from															");
		sb.append(" 	hr_sinsurance.v_ss_insurance_pay_this_month h				");
		sb.append(" left join hr_sinsurance.ss_insurance_pay_this_month_detail b on ");
		sb.append(" 	h.id = b.main_Id											");
		sb.append(" left join (													    ");
		sb.append(" 	select														");
		sb.append(" 		ssr.period ,										   	");
		sb.append(" 		ssd.insurance_id,										");
		sb.append(" 		ssd.staff_id,											");
		sb.append(" 		ssr.expense_id,											");
		sb.append(" 		ssr.social_type											");
		sb.append(" 	from														");
		sb.append(" 		c_yy_gz_aipoerp_db.social_security_record ssr			");
		sb.append(" 	left join c_yy_gz_aipoerp_db.social_security_detail ssd on	");
		sb.append(" 		ssr.id = ssd.foreignerKey								");
		sb.append(" 	where														");
		sb.append(" 		ssr.social_type = ?) t on							    ");
		sb.append(" 	h.staff_Id = t.staff_id										");
		sb.append(" 	and h.insurance_year_month = t.period						");
		sb.append(" left join hr_sinsurance.ss_insurance_type sit on				");
		sb.append(" 	b.pay_insurance_type_id = sit.id							");
		sb.append(" left join hr_sinsurance.ss_payment_institution spi on		  	");
		sb.append(" 	b.pi_id = spi.id											");
		sb.append(" where														  	");
		sb.append(" 	h.tenant_id = ?								                ");
		sb.append(" 	and h.dr = 0												");
		sb.append(" 	and b.insurance_year_month = ?						        ");
		sb.append(" 	and b.dr = 0												");
		sb.append(" 	and sit.dr = 0												");
		sb.append(" 	and sit.enable = 1											");
		sb.append(" 	and t.insurance_id is not null								");

		return sb.toString();
	}

	/**
	 * 生成社保缴交明细记录
	 *
	 * @param insurancePayDetailList
	 * @return
	 */
	private Map<String, SocialSecurityRecord> generateSocialSecurityRecord(
			List<InsurancePayDetail> insurancePayDetailList, GenerateSocialParam gsparam) {
		Map<String, List<InsurancePayDetail>> insurancePayDetailGroup = insurancePayDetailList.stream()
				.collect(Collectors.groupingBy(v -> v.getTaxOrgId() + "@" + v.getTaxDeptId() + "@" + v.getPiId() + "@"
						+ v.getInsuranceYearMonth()));

		Map<String, SocialSecurityRecord> socialSecurityRecords = Maps.newHashMap();
		for (String key : insurancePayDetailGroup.keySet()) {
			List<InsurancePayDetail> details = insurancePayDetailGroup.get(key);

			Map<String, List<InsurancePayDetail>> detailGroup = details.stream()
					.collect(Collectors.groupingBy(InsurancePayDetail::getStaffId)); // 按人员分组
			List<SocialSecurityDetail> detailList = Lists.newArrayList();
			for (String dkey : detailGroup.keySet()) {
				List<InsurancePayDetail> pdetails = detailGroup.get(dkey);
				SocialSecurityDetail ssdetail = generateSocialSecurityDetail(pdetails);
				detailList.add(ssdetail);
			}

			String[] keys = key.split("@"); // 财务组织、部门、缴交机构、缴交年月

			SocialSecurityRecord record = new SocialSecurityRecord();
			record.setPeriod(keys[3]);
			record.setFinanceOrg(keys[0]);
			record.setFinanceDept(keys[1]);
			record.setPaymentOrg(keys[2]);
			record.setSocialType(gsparam.getSocialType());
			record.setBusinessDate(LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
			record.setSocialSecurityDetailList(detailList);

			socialSecurityRecords.put(key, record);
		}
		return socialSecurityRecords;
	}

	/**
	 * 生成社保缴交合并记录明细
	 *
	 * @param pdetails
	 * @return
	 */
	private SocialSecurityDetail generateSocialSecurityDetail(List<InsurancePayDetail> pdetails) {
		SocialSecurityDetail detail = new SocialSecurityDetail();
		InsurancePayDetail ipd = pdetails.get(0);
		detail.setStaffId(ipd.getStaffId());
		detail.setOrgId(ipd.getTaxOrgId()); // 直接设置财务组织和财务部门
		detail.setDeptId(ipd.getTaxDeptId());
		detail.setInsuranceId(ipd.getInsuranceId());
		for (InsurancePayDetail insurancePayDetail : pdetails) {
			switch (insurancePayDetail.getInsuranceTypeName()) {
			case "养老":
				detail.setEndowmentInsuranceOwnBase(convertBigDecimal(insurancePayDetail.getPayPersonalBase()));
				detail.setEndowmentInsuranceOwnPay(calcOwnPay(insurancePayDetail));
				detail.setEndowmentInsuranceOrgBase(convertBigDecimal(insurancePayDetail.getPayCompanyBase()));
				detail.setEndowmentInsuranceOrgPay(calcOrgPay(insurancePayDetail));
				detail.setEndowmentInsuranceRadix(convertBigDecimal(insurancePayDetail.getRadix())); // 缴纳基数
				break;
			case "失业":
				detail.setUnemploymentOwnBase(convertBigDecimal(insurancePayDetail.getPayPersonalBase()));
				detail.setUnemploymentOwnPay(calcOwnPay(insurancePayDetail));
				detail.setUnemploymentOrgBase(convertBigDecimal(insurancePayDetail.getPayCompanyBase()));
				detail.setUnemploymentOrgPay(calcOrgPay(insurancePayDetail));
				detail.setUnemploymentRadix(convertBigDecimal(insurancePayDetail.getRadix()));
				break;
			case "工伤":
				detail.setOccupationalInjuryOwnBase(convertBigDecimal(insurancePayDetail.getPayPersonalBase()));
				detail.setOccupationalInjuryOwnPay(calcOwnPay(insurancePayDetail));
				detail.setOccupationalInjuryOrgBase(convertBigDecimal(insurancePayDetail.getPayCompanyBase()));
				detail.setOccupationalInjuryOrgPay(calcOrgPay(insurancePayDetail));
				detail.setOccupationalInjuryRadix(convertBigDecimal(insurancePayDetail.getRadix()));
				break;
			case "医疗":
				detail.setMedicalTreatmentOwnBase(convertBigDecimal(insurancePayDetail.getPayPersonalBase()));
				detail.setMedicalTreatmentOwnPay(calcOwnPay(insurancePayDetail));
				detail.setMedicalTreatmentOorgBase(convertBigDecimal(insurancePayDetail.getPayCompanyBase()));
				detail.setMedicalTreatmentOorgPay(calcOrgPay(insurancePayDetail));
				detail.setMedicalTreatmentRadix(convertBigDecimal(insurancePayDetail.getRadix()));
				break;
			case "补充医疗":
				detail.setRepMedicalTreatmentOwnBase(convertBigDecimal(insurancePayDetail.getPayPersonalBase()));
				detail.setRepMedicalTreatmentOwnPay(calcOwnPay(insurancePayDetail));
				detail.setRepMedicalTreatmentOrgBase(convertBigDecimal(insurancePayDetail.getPayCompanyBase()));
				detail.setRepMedicalTreatmentOrgPay(calcOrgPay(insurancePayDetail));
				detail.setRepMedicalTreatmentRadix(convertBigDecimal(insurancePayDetail.getRadix()));
				break;
			case "生育":
				detail.setBirthInsuranceOwnBase(convertBigDecimal(insurancePayDetail.getPayPersonalBase()));
				detail.setBirthInsuranceOwnPay(calcOwnPay(insurancePayDetail));
				detail.setBirthInsuranceOrgBase(convertBigDecimal(insurancePayDetail.getPayCompanyBase()));
				detail.setBirthInsuranceOrgPay(calcOrgPay(insurancePayDetail));
				detail.setBirthInsuranceRadix(convertBigDecimal(insurancePayDetail.getRadix()));
				break;
			case "大病":
				detail.setSeriousIllnessOwnBase(convertBigDecimal(insurancePayDetail.getPayPersonalBase()));
				detail.setSeriousIllnessOwnPay(calcOwnPay(insurancePayDetail));
				detail.setSeriousIllnessOrgBase(convertBigDecimal(insurancePayDetail.getPayCompanyBase()));
				detail.setSeriousIllnessOrgPay(calcOrgPay(insurancePayDetail));
				detail.setSeriousIllnessRadix(convertBigDecimal(insurancePayDetail.getRadix()));
				break;
			case "公积金":
				detail.setReservedFundsOwnBase(convertBigDecimal(insurancePayDetail.getPayPersonalBase()));
				detail.setReservedFundsOwnPay(calcOwnPay(insurancePayDetail));
				detail.setReservedFundsOrgBase(convertBigDecimal(insurancePayDetail.getPayCompanyBase()));
				detail.setReservedFundsOrgPay(calcOrgPay(insurancePayDetail));
				detail.setReservedFundsRadix(convertBigDecimal(insurancePayDetail.getRadix()));
				break;
			case "补充公积金":
				detail.setRepReservedFundsOwnBase(convertBigDecimal(insurancePayDetail.getPayPersonalBase()));
				detail.setRepReservedFundsOwnPay(calcOwnPay(insurancePayDetail));
				detail.setRepReservedFundsOrgBase(convertBigDecimal(insurancePayDetail.getPayCompanyBase()));
				detail.setRepReservedFundsOrgPay(calcOrgPay(insurancePayDetail));
				detail.setRepReservedFundsRadix(convertBigDecimal(insurancePayDetail.getRadix()));
				break;
			default:
				break;
			}
		}
		return detail;
	}

	/**
	 * 计算单位缴纳额
	 *
	 * @param insurancePayDetail
	 * @return
	 */
	private BigDecimal calcOrgPay(InsurancePayDetail insurancePayDetail) {
		BigDecimal amount = BigDecimal.ZERO;
		BigDecimal payAmount = convertBigDecimal(insurancePayDetail.getPayCompanyPayAmount());
		if (payAmount != null) {
			amount = amount.add(payAmount);
		}
		BigDecimal fixAmount = convertBigDecimal(insurancePayDetail.getPayCompanyFixedAmount());
		if (fixAmount != null) {
			amount = amount.add(fixAmount);
		}
		BigDecimal adjustmentAmount = convertBigDecimal(insurancePayDetail.getPayPersonalAdjustmentAmount());
		if (adjustmentAmount != null) {
			amount = amount.add(adjustmentAmount);
		}
		return amount;
	}

	/**
	 * 计算个人缴纳额
	 *
	 * @param insurancePayDetail
	 * @return
	 */
	private BigDecimal calcOwnPay(InsurancePayDetail insurancePayDetail) {
		BigDecimal amount = BigDecimal.ZERO;
		BigDecimal payAmount = convertBigDecimal(insurancePayDetail.getPayPersonalPayAmount());
		if (payAmount != null) {
			amount = amount.add(payAmount);
		}
		BigDecimal fixAmount = convertBigDecimal(insurancePayDetail.getPayPersonalFixedAmount());
		if (fixAmount != null) {
			amount = amount.add(fixAmount);
		}
		BigDecimal adjustmentAmount = convertBigDecimal(insurancePayDetail.getPayPersonalAdjustmentAmount());
		if (adjustmentAmount != null) {
			amount = amount.add(adjustmentAmount);
		}
		return amount;
	}

	/**
	 * 字符串转换成BigDecimal
	 *
	 * @param num
	 * @return
	 */
	private BigDecimal convertBigDecimal(String num) {
		if (StringUtils.isNotBlank(num)) {
			return new BigDecimal(num);
		}
		return null;
	}

	/**
	 * 生成社保福利支付单
	 *
	 * @param insurancePayDetailList 社保缴交明细数据
	 * @param gsparam                参数
	 * @param payOrgCfgMap           缴交机构相关财务配置
	 * @return
	 */
	private Map<String, CommonExpenseBillVO> generateSecurityPayment(List<InsurancePayDetail> insurancePayDetailList,
			GenerateSocialParam gsparam, Map<String, Map<String, Object>> payOrgCfgMap) {
		CurUserInfo userInfo = appContext.getCurUserInfo();

		List<String> vendorIds = payOrgCfgMap.values().stream().map(m -> {
			Object supplierId = m.get("supplierId");
			if (supplierId != null) {
				return supplierId.toString();
			}
			return null;
		}).filter(Objects::nonNull).distinct().collect(Collectors.toList());

		Map<String, VendorInfo> vendorMap = vendorQryervice.queryByIds(vendorIds);

		String bustype = "";
		if ("社保".equals(gsparam.getSocialType())) {
			FieldRef fieldRef = fieldRefService.getFieldRefByKkField("securityBustype");
			if (fieldRef == null || StringUtils.isEmpty(fieldRef.getRefField())) {
				throw new RuntimeException(
						"没有配置社保支付单交易类型编码【securityBustype】，请检查【数字化建模>字段映射>客开系统配置>字段映射】是否配置了【securityBustype】！");
			}
			bustype = fieldRef.getRefField();
		} else {
			FieldRef fieldRef = fieldRefService.getFieldRefByKkField("fundBustype");
			if (fieldRef == null || StringUtils.isEmpty(fieldRef.getRefField())) {
				throw new RuntimeException(
						"没有配置公积金支付单交易类型编码【fundBustype】，请检查【数字化建模>字段映射>客开系统配置>字段映射】是否配置了【fundBustype】！");
			}
			bustype = fieldRef.getRefField();
		}

		// 1、社保缴交数据，按财务组织、部门、缴交机构、缴交年月分组，再按险种合计生成社保福利支付单明细
		Map<String, List<Expensebillb>> expensebillbMap = generateExpensebillb(insurancePayDetailList, userInfo,
				payOrgCfgMap, gsparam);
		if (expensebillbMap.isEmpty()) {
			// TODO 没有生成社保福利支付单明细数据，可能缴交金额都是0
			if ("社保".equals(gsparam.getSocialType())) {
				throw new RuntimeException("缴交数据合计金额为0");
			} else {
				throw new RuntimeException("没有查询到可以生成公积金支付单的缴交数据");
			}
		}

		// 2、根据社保福利支付单明细，生成费用分摊明细
		Map<String, List<Expapportion>> expapportionMap = generateExpapportion(expensebillbMap);

		// 3、根据社保福利支付单明细，生成结算信息
		Map<String, List<Expsettleinfo>> expsettleinfoMap = generateExpsettleinfo(expensebillbMap, payOrgCfgMap,
				vendorMap);

		// 4、创建社保福利支付单
		Map<String, CommonExpenseBillVO> socialSecurityPayments = Maps.newHashMap();
		for (String key : expensebillbMap.keySet()) {
			String[] keys = key.split("@"); // 财务组织、部门、缴交机构、缴交年月
			List<Expensebillb> expensebillbs = expensebillbMap.get(key);

			Map<String, Object> payOrgCfg = payOrgCfgMap.getOrDefault(keys[2], Collections.emptyMap());

			CommonExpenseBillVO socialSecurityPayment = new CommonExpenseBillVO();
			socialSecurityPayment.setDcostdate(userInfo.getBusDate());
			socialSecurityPayment.setVouchdate(userInfo.getBusDate());
			socialSecurityPayment.setVfinacedeptid(keys[1]);
			socialSecurityPayment.setCfinaceorg(keys[0]);
			socialSecurityPayment.setCaccountorg(keys[0]);
			socialSecurityPayment.setBustype(bustype); // 社保福利支付单/公积金支付单
			socialSecurityPayment.setPk_handlepsn(userInfo.getStaffId());
			socialSecurityPayment.setVhandledeptid(userInfo.getDeptId());
			socialSecurityPayment.setChandleorg(userInfo.getOrgId());
			socialSecurityPayment.setDnatexchratedate(userInfo.getBusDate());
			BigDecimal nexpensemny = BigDecimal.ZERO; // 不含税总额
			BigDecimal nnatexpensemny = BigDecimal.ZERO; // 不含税总额-本币
			BigDecimal nsummny = BigDecimal.ZERO; // 价税总额
			BigDecimal nnatsummny = BigDecimal.ZERO; // 价税总额-本币
			for (Expensebillb expensebillb : expensebillbs) {
				if (StringUtils.isNotBlank(expensebillb.getNexpensemny())) {
					nexpensemny = nexpensemny.add(new BigDecimal(expensebillb.getNexpensemny()));
				}
				if (StringUtils.isNotBlank(expensebillb.getNnatexpensemny())) {
					nnatexpensemny = nnatexpensemny.add(new BigDecimal(expensebillb.getNnatexpensemny()));
				}
				if (StringUtils.isNotBlank(expensebillb.getNsummny())) {
					nsummny = nsummny.add(new BigDecimal(expensebillb.getNsummny()));
				}
				if (StringUtils.isNotBlank(expensebillb.getNnatsummny())) {
					nnatsummny = nnatsummny.add(new BigDecimal(expensebillb.getNnatsummny()));
				}
			}
			socialSecurityPayment.setNexpensemny(nexpensemny.setScale(2, RoundingMode.HALF_UP).toString());
			socialSecurityPayment.setNnatexpensemny(nnatexpensemny.setScale(2, RoundingMode.HALF_UP).toString());
			socialSecurityPayment.setNsummny(nsummny.setScale(2, RoundingMode.HALF_UP).toString());
			socialSecurityPayment.setNnatsummny(nnatsummny.setScale(2, RoundingMode.HALF_UP).toString());
			socialSecurityPayment.setNshouldpaymny(nsummny.setScale(2, RoundingMode.HALF_UP).toString());
			socialSecurityPayment.setNnatshouldpaymny(nnatsummny.setScale(2, RoundingMode.HALF_UP).toString());
			socialSecurityPayment.setPk_cusdoc(payOrgCfg.getOrDefault("supplierId", "").toString());
			socialSecurityPayment.setPk_cusdoc_code(payOrgCfg.getOrDefault("supplierCode", "").toString());

			String explain = payOrgCfg.getOrDefault("explain", "").toString();

			if (StringUtils.isNotBlank(explain)) {
				explain = explain.replace("{period}", keys[3].replace("-", "年") + "月");
				socialSecurityPayment.setVreason(explain);
			} else if ("社保".equals(gsparam.getSocialType())) {
				socialSecurityPayment.setVreason(keys[3] + "期间社保福利支付");
			} else {
				socialSecurityPayment.setVreason(keys[3] + "期间公积金支付");
			}
			// 设置用户id，接口查询会报错
//            socialSecurityPayment.setCreatorId(userInfo.getUserId());
//            socialSecurityPayment.setCreatorId("7cdf19ac-4902-497e-8690-45f3e9181972");
			socialSecurityPayment.setCreator_code(userInfo.getUserCode());

			// 子表数据
			socialSecurityPayment.setExpensebillbs(expensebillbs);
			socialSecurityPayment.setExpapportions(expapportionMap.get(key));
			socialSecurityPayment.setExpsettleinfos(expsettleinfoMap.get(key));
			if (CollectionUtils.isNotEmpty(expsettleinfoMap.get(key))) { // 社保结算单付款附言
				Expsettleinfo expsettleinfo = expsettleinfoMap.get(key).get(0);
				JSONObject expsettleinfoDcs = new JSONObject();
				expsettleinfoDcs.put("BX76", socialSecurityPayment.getVreason());
				expsettleinfo.setExpsettleinfoDcs(expsettleinfoDcs);
			}

			JSONObject expensebillDcs = new JSONObject();
			expensebillDcs.put("tz_payorg", keys[2]); // 缴交机构

			socialSecurityPayment.setExpensebillDcs(expensebillDcs);

			socialSecurityPayment.setExtend2(keys[3]); // 扩展字段-期间

			socialSecurityPayments.put(key, socialSecurityPayment);
		}
		return socialSecurityPayments;
	}

	/**
	 * 根据社保福利支付单明细，生成结算信息
	 * 
	 * @param expensebillbMap
	 * @param payOrgCfgMap
	 * @return
	 */
	private Map<String, List<Expsettleinfo>> generateExpsettleinfo(Map<String, List<Expensebillb>> expensebillbMap,
			Map<String, Map<String, Object>> payOrgCfgMap, Map<String, VendorInfo> vendorMap) {
		Map<String, List<Expsettleinfo>> expsettleinfoMap = Maps.newHashMap();
		for (Entry<String, List<Expensebillb>> entry : expensebillbMap.entrySet()) {
			String[] keys = entry.getKey().split("@");// 拆分【财务组织id、财务部门id、缴交机构id、缴交年月】
			List<Expensebillb> values = entry.getValue();
			BigDecimal nsummny = BigDecimal.ZERO; // 结算合计付款金额
			for (Expensebillb expensebillb : values) {
				if (StringUtils.isNotBlank(expensebillb.getNsummny())) {
					nsummny = nsummny.add(new BigDecimal(expensebillb.getNsummny()));
				}
			}
			String nsummnyStr = nsummny.toString();

			Map<String, Object> payOrgCfg = payOrgCfgMap.getOrDefault(keys[2], Collections.emptyMap());

			Expsettleinfo expsettleinfo = new Expsettleinfo();
			Expensebillb expensebillb = values.get(0);
			VendorInfo vendorInfo = vendorMap.get(expensebillb.getPk_cusdoc());
			VendorBank vendorBank = getDefaultBank(vendorInfo);
			if (vendorBank != null) {
				expsettleinfo.setVbankaccount(vendorBank.getAccount()); // TODO 收款方账号
				expsettleinfo.setVbankaccname(
						vendorBank.getAccountname() != null ? vendorBank.getAccountname().getZh_CN() : ""); // TODO
				// 收款方户名
				expsettleinfo.setPk_bankdoc(vendorBank.getOpenaccountbank()); // TODO 收款方开户行
				expsettleinfo.setPk_banktype(vendorBank.getBank()); // TODO 收款银行类别
				expsettleinfo.setPk_cusdocbank(vendorBank.getId()); // TODO 供应商银行账户id
				expsettleinfo.setPk_cusdocbank_code(null);// TODO 供应商银行账户编码(付款类型igathertype为1时，id和编码必填一项)
				expsettleinfo.setAccttype(vendorBank.getAccountType());
			}
			expsettleinfo.setPk_cusdoc(vendorInfo.getId()); // TODO 供应商id(付款类型igathertype为1时，id和编码必填一项)
			expsettleinfo.setPk_cusdoc_code(vendorInfo.getCode()); // TODO 供应商编码(付款类型igathertype为1时，id和编码必填一项)
			expsettleinfo.setIgathertype("1"); // TODO 收款类型(0:个人;1:供应商;2:客户)
			expsettleinfo.setPk_balatype(payOrgCfg.getOrDefault("pkBalatype", "").toString()); // 结算方式id(结算方式编码和id必填一项)
			if (payOrgCfg.get("pkBalatype") == null) {
				expsettleinfo.setPk_balatype_code("system_0001"); // 如果没有配置默认结算方，则默认银行转账(结算方式编码和id必填一项)
			}
			expsettleinfo.setBalatypesrvattr("0"); // TODO 结算方式业务属性(0:银行业务;1:现金业务）
			expsettleinfo.setCenterpriseorg(expensebillb.getCfinaceorg());
			expsettleinfo.setPk_enterprisebankacct(payOrgCfg.getOrDefault("enterprisebank", "").toString());// 企业银行账户(支持id和code，结算方式为银行转账时必填)
			expsettleinfo.setVbankaccount_opp(payOrgCfg.getOrDefault("account", "").toString()); // 付款银行账号(结算方式为银行转账时必填)
			expsettleinfo.setAccttype_opp(payOrgCfg.getOrDefault("acctType", "").toString());// 付款账户类型（0:基本;1:一般;2:临时;3:专用）
			expsettleinfo.setVbankaccname_opp(payOrgCfg.getOrDefault("acctName", "").toString()); // 付款账户户名
			expsettleinfo.setPk_banktype_opp(payOrgCfg.getOrDefault("bank", "").toString()); // 付款银行类别(支持id和code，结算方式为银行转账时必填)
			expsettleinfo.setPk_bankdoc_opp(payOrgCfg.getOrDefault("bankNumber", "").toString()); // 付款开户行(支持id和code，结算方式为银行转账时必填)
			expsettleinfo.setPk_enterprisecashacct(null); // TODO 企业现金账户(支持id和code，结算方式为现金时必填)
			expsettleinfo.setDnatexchratedate(expensebillb.getDnatexchratedate());
			expsettleinfo.setNsummny(nsummnyStr);
			expsettleinfo.setNsettlesummny(nsummnyStr);
			expsettleinfo.setNnatsettlesummny(nsummnyStr);

			expsettleinfoMap.put(entry.getKey(), Arrays.asList(expsettleinfo));

		}
		return expsettleinfoMap;
	}

	/**
	 * 获取供应商默认银行账户，如果不存在默认银行账户，取第一条
	 *
	 * @param vendorInfo
	 * @return
	 */
	private VendorBank getDefaultBank(VendorInfo vendorInfo) {
		if (vendorInfo == null || CollectionUtils.isEmpty(vendorInfo.getVendorbanks())) {
			return null;
		}
		return vendorInfo.getVendorbanks().stream().filter(VendorBank::isDefaultbank).findFirst()
				.orElse(vendorInfo.getVendorbanks().get(0));
	}

	/**
	 * 根据社保福利支付单明细，生成费用分摊明细
	 *
	 * @param expensebillbMap
	 * @return
	 */
	private Map<String, List<Expapportion>> generateExpapportion(Map<String, List<Expensebillb>> expensebillbMap) {
		Map<String, List<Expapportion>> expapportionMap = Maps.newHashMap();
		for (Entry<String, List<Expensebillb>> entry : expensebillbMap.entrySet()) {
			List<Expensebillb> values = entry.getValue();
			List<Expapportion> expapportionList = values.stream().map(v -> {
				Expapportion expapportion = new Expapportion();
				expapportion.setVfinacedeptid(v.getVfinacedeptid());
				expapportion.setCfinaceorg(v.getCfinaceorg());
				expapportion.setCaccountorg(v.getCaccountorg());
				expapportion.setPk_busimemo(v.getPk_busimemo());
				expapportion.setDnatexchratedate(v.getDnatexchratedate());
				expapportion.setNapportmny(v.getNsummny());
				expapportion.setNnatapportmny(v.getNnatsummny());
				expapportion.setNapportnotaxmny(v.getNexpensemny());
				expapportion.setNnatapportnotaxmny(v.getNnatexpensemny());
				return expapportion;
			}).collect(Collectors.toList());
			expapportionMap.put(entry.getKey(), expapportionList);
		}
		return expapportionMap;
	}

	/**
	 * 社保缴交数据，按财务组织、部门、缴交机构、缴交年月分组，再按险种合计生成社保福利支付单明细
	 *
	 * @param insurancePayDetailList 社保缴交明细数据
	 * @param userInfo               当前登录用户信息
	 * @param vendorInfo             社保支付单供应商信息
	 * @return
	 */
	private Map<String, List<Expensebillb>> generateExpensebillb(List<InsurancePayDetail> insurancePayDetailList,
			CurUserInfo userInfo, Map<String, Map<String, Object>> payOrgCfgMap, GenerateSocialParam gsparam) {

		// 查询费用项目
		Map<String, ExpenseItem> expenseMap = expenseItemService.getAllExpenseItem();

		// 查询字段映射
		Map<String, FieldRef> fieldRefMap = fieldRefService.getFieldRef();
		Map<String, String> kkKeyMap = fieldRefService.getFieldRefKkKey();

		// 查询缴交机构
		Map<String, Map<String, Object>> paymentInstitutionMap = paymentInstitutionService.queryAllForMap();

		Map<String, List<InsurancePayDetail>> insurancePayDetailGroup = insurancePayDetailList.stream()
				.collect(Collectors.groupingBy(v -> v.getTaxOrgId() + "@" + v.getTaxDeptId() + "@" + v.getPiId() + "@"
						+ v.getInsuranceYearMonth()));

		Map<String, List<Expensebillb>> expensebillbMap = Maps.newHashMap();

		for (Entry<String, List<InsurancePayDetail>> entry : insurancePayDetailGroup.entrySet()) {

			List<InsurancePayDetail> payDetails = entry.getValue();
			Map<String, List<InsurancePayDetail>> payDetailGroup = payDetails.stream()
					.collect(Collectors.groupingBy(v -> { // 按险种分组，其中补充医疗归类到医疗，补充公积金归类到公积金
						String insuranceTypeName = v.getInsuranceTypeName(); // 险种名称（险种没有编码，只能通过名称判断）
						FieldRef fieldRef = fieldRefMap.get(insuranceTypeName);
						if (fieldRef != null) {
							insuranceTypeName = fieldRef.getKkField();
						}
						if ("补充医疗".equals(insuranceTypeName)) {
							return "医疗";
						}
						if ("补充公积金".equals(insuranceTypeName)) {
							return "公积金";
						}
						return insuranceTypeName;
					}));

			Map<String, List<InsurancePayDetail>> finalDetailGroup = Maps.newHashMap();
			if ("社保".equals(gsparam.getSocialType())) {
				Map<String, Object> paymentInstitution = paymentInstitutionMap.get(payDetails.get(0).getPiId());
				if (paymentInstitution != null && paymentInstitution.get("piType") != null
						&& paymentInstitution.get("piType").toString().equals("0")) {
					// 外部机构
					finalDetailGroup.putAll(payDetailGroup);
				} else {
					payDetailGroup.remove("公积金");
					finalDetailGroup.putAll(payDetailGroup);
				}
			} else {
				Map<String, Object> paymentInstitution = paymentInstitutionMap.get(payDetails.get(0).getPiId());
				if (paymentInstitution != null && paymentInstitution.get("piType") != null
						&& paymentInstitution.get("piType").toString().equals("1")) {
					// 外部机构（不单独生成公积金支付单）
					List<InsurancePayDetail> list = payDetailGroup.get("公积金");
					if (CollectionUtils.isNotEmpty(list)) {
						finalDetailGroup.put("公积金", list);
					}
				}
			}

			Map<String, Object> payOrgCfg = payOrgCfgMap.getOrDefault(payDetails.get(0).getPiId(), Maps.newHashMap());

			List<Expensebillb> expensebillbs = Lists.newArrayList();
			for (String key : finalDetailGroup.keySet()) {
				List<InsurancePayDetail> payDetailList = payDetailGroup.get(key);
				BigDecimal personalAmount = BigDecimal.ZERO; // 个人缴交合计
				BigDecimal companyAmount = BigDecimal.ZERO; // 单位缴交合计
				for (InsurancePayDetail payDetail : payDetailList) {
					if (StringUtils.isNotBlank(payDetail.getPayPersonalPayAmount())) {
						// 个人缴交额
						personalAmount = personalAmount.add(new BigDecimal(payDetail.getPayPersonalPayAmount()));
					}
//					if (StringUtils.isNotBlank(payDetail.getPayPersonalFixedAmount())) {
//						// 个人固定缴交额
//						personalAmount = personalAmount.add(new BigDecimal(payDetail.getPayPersonalFixedAmount()));
//					}
//					if (StringUtils.isNotBlank(payDetail.getPayPersonalAdjustmentAmount())) {
//						// 个人缴交调整额
//						personalAmount = personalAmount.add(new BigDecimal(payDetail.getPayPersonalAdjustmentAmount()));
//					}
					if (StringUtils.isNotBlank(payDetail.getPayCompanyPayAmount())) {
						// 单位缴交额
						companyAmount = companyAmount.add(new BigDecimal(payDetail.getPayCompanyPayAmount()));
					}
//					if (StringUtils.isNotBlank(payDetail.getPayCompanyFixedAmount())) {
//						// 单位固定缴交额
//						companyAmount = companyAmount.add(new BigDecimal(payDetail.getPayCompanyFixedAmount()));
//					}
//					if (StringUtils.isNotBlank(payDetail.getPayCompanyAdjustmentAmount())) {
//						// 单位缴交调整额
//						companyAmount = companyAmount.add(new BigDecimal(payDetail.getPayCompanyAdjustmentAmount()));
//					}
				}

				String ekey = entry.getKey();
				String[] ekeys = ekey.split("@"); // 拆分【财务组织id、财务部门id、缴交机构id、缴交年月】

				ExpenseItem expenseP = getExpense(key, expenseMap, "P", kkKeyMap); // 个人保险
				String personalAmountStr = personalAmount.setScale(2, RoundingMode.HALF_UP).toString();
				if (expenseP != null) {
					Expensebillb expensebillbP = new Expensebillb(); // 创建报销单明细（个人保险项）
					// 费用项目信息
					expensebillbP.setPk_busimemo(expenseP.getId());
					expensebillbP.setDnatexchratedate(userInfo.getBusDate());
					expensebillbP.setPk_handlepsn(userInfo.getStaffId());
					expensebillbP.setVhandledeptid(userInfo.getDeptId());
					expensebillbP.setChandleorg(userInfo.getOrgId());
					expensebillbP.setCaccountorg(ekeys[0]);
					expensebillbP.setCfinaceorg(ekeys[0]);
					expensebillbP.setVfinacedeptid(ekeys[1]);
					expensebillbP.setPk_cusdoc(payOrgCfg.getOrDefault("supplierId", "").toString()); // 供应商id
					expensebillbP.setPk_cusdoc_code(payOrgCfg.getOrDefault("supplierCode", "").toString()); // 供应商编码
					expensebillbP.setNexpensemny(personalAmountStr);
					expensebillbP.setNnatexpensemny(personalAmountStr);
					expensebillbP.setNsummny(personalAmountStr);
					expensebillbP.setNnatsummny(personalAmountStr);
					expensebillbP.setNshouldpaymny(personalAmountStr);
					expensebillbP.setNnatshouldpaymny(personalAmountStr);
					expensebillbP.setNpaymentmny(personalAmountStr);
					expensebillbP.setNnatpaymentmny(personalAmountStr);

					expensebillbs.add(expensebillbP);
				} else {
					// 费用项目为空则跳过
					log.error("查询费用项目为空【{}】P，personalAmount：{}", key, personalAmountStr);
				}

				ExpenseItem expenseC = getExpense(key, expenseMap, "C", kkKeyMap);// 单位保险
				String companyAmountStr = companyAmount.setScale(2, RoundingMode.HALF_UP).toString();
				if (expenseC != null) {
					Expensebillb expensebillbC = new Expensebillb(); // 创建报销单明细（单位保险项）
					// 费用项目信息
					expensebillbC.setPk_busimemo(expenseC.getId());
					expensebillbC.setDnatexchratedate(userInfo.getBusDate());
					expensebillbC.setPk_handlepsn(userInfo.getStaffId());
					expensebillbC.setVhandledeptid(userInfo.getDeptId());
					expensebillbC.setChandleorg(userInfo.getOrgId());
					expensebillbC.setCaccountorg(ekeys[0]);
					expensebillbC.setCfinaceorg(ekeys[0]);
					expensebillbC.setVfinacedeptid(ekeys[1]);
					expensebillbC.setPk_cusdoc(payOrgCfg.getOrDefault("supplierId", "").toString()); // 供应商id
					expensebillbC.setPk_cusdoc_code(payOrgCfg.getOrDefault("supplierCode", "").toString()); // 供应商编码
					expensebillbC.setNexpensemny(companyAmountStr);
					expensebillbC.setNnatexpensemny(companyAmountStr);
					expensebillbC.setNsummny(companyAmountStr);
					expensebillbC.setNnatsummny(companyAmountStr);
					expensebillbC.setNshouldpaymny(companyAmountStr);
					expensebillbC.setNnatshouldpaymny(companyAmountStr);
					expensebillbC.setNpaymentmny(companyAmountStr);
					expensebillbC.setNnatpaymentmny(companyAmountStr);

					expensebillbs.add(expensebillbC);
				} else {
					// 费用项目为空则跳过
					log.error("查询费用项目为空【{}】C，companyAmount：{}", key, companyAmountStr);
				}
			}

			if (!expensebillbs.isEmpty()) {
				expensebillbMap.put(entry.getKey(), expensebillbs);
			}
		}
		return expensebillbMap;
	}

	/**
	 * 查找社保费用项目
	 *
	 * @param key
	 * @param expenseMap
	 * @param type       P个人，C单位
	 * @param kkKeyMap   TODO
	 * @return
	 */
	private ExpenseItem getExpense(String key, Map<String, ExpenseItem> expenseMap, String type,
			Map<String, String> kkKeyMap) {
		switch (key) {
		case "养老":
			if ("P".equals(type)) {
				String feeCode = getFeeCode(kkKeyMap, ExpenseEnum.ENDOWMENT_PERSONAL);
				return expenseMap.get(feeCode);
			} else if ("C".equals(type)) {
				String feeCode = getFeeCode(kkKeyMap, ExpenseEnum.ENDOWMENT_COMPANY);
				return expenseMap.get(feeCode);
			}
			break;
		case "失业":
			if ("P".equals(type)) {
				String feeCode = getFeeCode(kkKeyMap, ExpenseEnum.UNEMPLOYMENT_PERSONAL);
				return expenseMap.get(feeCode);
			} else if ("C".equals(type)) {
				String feeCode = getFeeCode(kkKeyMap, ExpenseEnum.UNEMPLOYMENT_COMPANY);
				return expenseMap.get(feeCode);
			}
			break;
		case "工伤":
			if ("P".equals(type)) {
				String feeCode = getFeeCode(kkKeyMap, ExpenseEnum.OCCUPATIONAL_PERSONAL);
				return expenseMap.get(feeCode);
			} else if ("C".equals(type)) {
				String feeCode = getFeeCode(kkKeyMap, ExpenseEnum.OCCUPATIONAL_COMPANY);
				return expenseMap.get(feeCode);
			}
			break;
		case "医疗":
			if ("P".equals(type)) {
				String feeCode = getFeeCode(kkKeyMap, ExpenseEnum.MEDICAL_PERSONAL);
				return expenseMap.get(feeCode);
			} else if ("C".equals(type)) {
				String feeCode = getFeeCode(kkKeyMap, ExpenseEnum.MEDICAL_COMPANY);
				return expenseMap.get(feeCode);
			}
			break;
		case "生育":
			if ("P".equals(type)) {
				String feeCode = getFeeCode(kkKeyMap, ExpenseEnum.BIRTH_PERSONAL);
				return expenseMap.get(feeCode);
			} else if ("C".equals(type)) {
				String feeCode = getFeeCode(kkKeyMap, ExpenseEnum.BIRTH_COMPANY);
				return expenseMap.get(feeCode);
			}
			break;
		case "大病":
			if ("P".equals(type)) {
				String feeCode = getFeeCode(kkKeyMap, ExpenseEnum.SERIOUS_PERSONAL);
				return expenseMap.get(feeCode);
			} else if ("C".equals(type)) {
				String feeCode = getFeeCode(kkKeyMap, ExpenseEnum.SERIOUS_COMPANY);
				return expenseMap.get(feeCode);
			}
			break;
		case "服务费":
			if ("P".equals(type)) {
				String feeCode = getFeeCode(kkKeyMap, ExpenseEnum.SERVICE_CHARGE_PERSONAL);
				return expenseMap.get(feeCode);
			} else if ("C".equals(type)) {
				String feeCode = getFeeCode(kkKeyMap, ExpenseEnum.SERVICE_CHARGE_COMPANY);
				return expenseMap.get(feeCode);
			}
			break;
		case "公积金":
			if ("P".equals(type)) {
				String feeCode = getFeeCode(kkKeyMap, ExpenseEnum.FUNDS_PERSONAL);
				return expenseMap.get(feeCode);
			} else if ("C".equals(type)) {
				String feeCode = getFeeCode(kkKeyMap, ExpenseEnum.FUNDS_COMPANY);
				return expenseMap.get(feeCode);
			}
			break;
		default:
			break;
		}
		return null;
	}

	/**
	 * 根据字段映射配置获取费用项目编码，因为测试环境和正式环境费用项目编码不一致。且正式环境费用项目编码不让修改。所以又加了编码的配置。前期遗留问题变相解决方案
	 * 
	 * @param kkKeyMap
	 * @param expense
	 * @return
	 */
	private String getFeeCode(Map<String, String> kkKeyMap, ExpenseEnum expense) {
		String feeCode = kkKeyMap.get(expense.getName());
		if (StringUtils.isBlank(feeCode)) {
			throw new RuntimeException(String.format("根据【%s】未获取到费用编码，请检查【数字化建模>字段映射>客开系统配置>字段映射】是否配置了【%s】！",
					expense.getName(), expense.getName()));
		}
		return feeCode;
	}

	/**
	 * 生成查询社保缴交明细的sql
	 *
	 * @return
	 */
	private String createQueryInsurancePayDetailSql() {
		StringBuilder sb = new StringBuilder();
		sb.append(" select                                                             ");
		sb.append(" 	h.id insuranceId,											   ");
		sb.append(" 	bs.name staffName,											   ");
		sb.append(" 	h.staff_Id staffId,											   ");
		sb.append(" 	b.insurance_year_month insuranceYearMonth,					   ");
		sb.append(" 	b.pi_id piId,												   ");
		sb.append(" 	spi.pi_code piCode,											   ");
		sb.append(" 	spi.pi_name piName,											   ");
		sb.append(" 	b.pay_insurance_type_id payInsuranceTypeId,					   ");
		sb.append(" 	sit.type_name insuranceTypeName,							   ");
		sb.append(" 	b.radix,													   ");
		sb.append(" 	b.pay_personal_base payPersonalBase,						   ");
		sb.append(" 	b.pay_personal_pay_amount payPersonalPayAmount,				   ");
		sb.append(" 	b.pay_personal_fixed_amount payPersonalFixedAmount,			   ");
		sb.append(" 	b.pay_personal_adjustment_amount payPersonalAdjustmentAmount,  ");
		sb.append(" 	b.pay_company_base payCompanyBase,							   ");
		sb.append(" 	b.pay_company_pay_amount payCompanyPayAmount,				   ");
		sb.append(" 	b.pay_company_fixed_amount payCompanyFixedAmount,			   ");
		sb.append(" 	b.pay_company_adjustment_amount payCompanyAdjustmentAmount	   ");
		sb.append(" from															   ");
		sb.append(" 	hr_sinsurance.v_ss_insurance_pay_this_month h					   ");
		sb.append(" left join iuap_apdoc_basedoc.bd_staff bs on						   ");
		sb.append(" 	h.staff_Id = bs.id											   ");
		sb.append(" left join hr_sinsurance.ss_insurance_pay_this_month_detail b on	   ");
		sb.append(" 	h.id = b.main_Id											   ");
		sb.append(" left join (														   ");
		sb.append(" 	select														   ");
		sb.append(" 		ssd.insurance_id,										   ");
		sb.append(" 		ssr.period,										   		   ");
		sb.append(" 		ssd.staff_id										       ");
		sb.append(" 	from														   ");
		sb.append(" 		c_yy_gz_aipoerp_db.social_security_record ssr			   ");
		sb.append(" 	left join c_yy_gz_aipoerp_db.social_security_detail ssd on	   ");
		sb.append(" 		ssr.id = ssd.foreignerKey								   ");
		sb.append(" 	where														   ");
		sb.append(" 		ssr.social_type = ?) t on							       ");
		sb.append(" 	h.staff_Id = t.staff_id										   ");
		sb.append(" 	and h.insurance_year_month = t.period						   ");
		sb.append(" left join hr_sinsurance.ss_insurance_type sit on				   ");
		sb.append(" 	b.pay_insurance_type_id = sit.id							   ");
		sb.append(" left join hr_sinsurance.ss_payment_institution spi on			   ");
		sb.append(" 	b.pi_id = spi.id											   ");
		sb.append(" where															   ");
		sb.append(" 	h.tenant_id = ?								                   ");
		sb.append(" 	and h.dr = 0												   ");
		sb.append(" 	and b.insurance_year_month = ?						           ");
		sb.append(" 	and b.dr = 0												   ");
		sb.append(" 	and sit.dr = 0												   ");
		sb.append(" 	and sit.enable = 1											   ");
		sb.append(" 	and t.insurance_id is null									   ");

		sb.append(" select                                                           ");
		sb.append("    t.insurance_id,												  ");
		sb.append(" 	h.id insuranceId,											  ");
		sb.append(" 	bs.name staffName,											  ");
		sb.append(" 	h.staff_Id staffId,											  ");
		sb.append(" 	b.insurance_year_month insuranceYearMonth,					  ");
		sb.append(" 	b.pi_id piId,												  ");
		sb.append(" 	spi.pi_code piCode,											  ");
		sb.append(" 	spi.pi_name piName,											  ");
		sb.append(" 	b.pay_insurance_type_id payInsuranceTypeId,					  ");
		sb.append(" 	sit.type_name insuranceTypeName,							  ");
		sb.append(" 	b.radix,													  ");
		sb.append(" 	b.pay_personal_base payPersonalBase,						  ");
		sb.append(" 	b.pay_personal_pay_amount payPersonalPayAmount,				  ");
		sb.append(" 	b.pay_personal_fixed_amount payPersonalFixedAmount,			  ");
		sb.append(" 	b.pay_personal_adjustment_amount payPersonalAdjustmentAmount, ");
		sb.append(" 	b.pay_company_base payCompanyBase,							  ");
		sb.append(" 	b.pay_company_pay_amount payCompanyPayAmount,				  ");
		sb.append(" 	b.pay_company_fixed_amount payCompanyFixedAmount,			  ");
		sb.append(" 	b.pay_company_adjustment_amount payCompanyAdjustmentAmount	  ");
		sb.append(" from															  ");
		sb.append(" 	hr_sinsurance.v_ss_insurance_pay_this_month h				  ");
		sb.append(" left join iuap_apdoc_basedoc.bd_staff bs on					      ");
		sb.append(" 	h.staff_Id = bs.id											  ");
		sb.append(" left join hr_sinsurance.ss_insurance_pay_this_month_detail b on   ");
		sb.append(" 	h.id = b.main_Id											  ");
		sb.append(" left join (													      ");
		sb.append(" 	select														  ");
		sb.append(" 		ssd.insurance_id,										  ");
		sb.append(" 		ssr.period,										   		  ");
		sb.append(" 		ssd.staff_id,											  ");
		sb.append(" 		ssr.payment_org											  ");
		sb.append(" 	from														  ");
		sb.append(" 		c_yy_gz_aipoerp_db.social_security_record ssr			  ");
		sb.append(" 	left join c_yy_gz_aipoerp_db.social_security_detail ssd on	  ");
		sb.append(" 		ssr.id = ssd.foreignerKey								  ");
		sb.append(" 	where														  ");
		sb.append(" 		ssr.social_type = ?) t on							      ");
		sb.append(" 	h.staff_Id = t.staff_id										  ");
		sb.append(" 	and h.insurance_year_month = t.period						  ");
		sb.append(" 	and b.pi_id = t.payment_org									  ");
		sb.append(" left join hr_sinsurance.ss_insurance_type sit on				  ");
		sb.append(" 	b.pay_insurance_type_id = sit.id							  ");
		sb.append(" left join hr_sinsurance.ss_payment_institution spi on			  ");
		sb.append(" 	b.pi_id = spi.id											  ");
		sb.append(" where															  ");
		sb.append(" 	h.tenant_id = ?								                  ");
		sb.append(" 	and h.dr = 0												  ");
		sb.append(" 	and b.insurance_year_month = ?						          ");
		sb.append(" 	and b.dr = 0												  ");
		sb.append(" 	and sit.dr = 0												  ");
		sb.append(" 	and sit.enable = 1											  ");
		sb.append(" 	and t.insurance_id is null									  ");

		return sb.toString();
	}
}
