package com.yonyou.ucf.mdf.rbsm.service.impl;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.imeta.orm.schema.QueryCondition;
import org.imeta.orm.schema.QueryConditionGroup;
import org.imeta.orm.schema.QueryJoin;
import org.imeta.orm.schema.QuerySchema;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import com.yonyou.aipierp.enums.ActionEnum;
import com.yonyou.ucf.mdf.rbsm.constants.PayTypeEnum;
import com.yonyou.ucf.mdf.rbsm.model.CommonExpenseBillVO;
import com.yonyou.ucf.mdf.rbsm.model.CommonExpenseSaveParam;
import com.yonyou.ucf.mdf.rbsm.model.CurUserInfo;
import com.yonyou.ucf.mdf.rbsm.model.Expapportion;
import com.yonyou.ucf.mdf.rbsm.model.ExpenseItem;
import com.yonyou.ucf.mdf.rbsm.model.Expensebillb;
import com.yonyou.ucf.mdf.rbsm.model.Expsettleinfo;
import com.yonyou.ucf.mdf.rbsm.model.FieldRef;
import com.yonyou.ucf.mdf.rbsm.model.IncomeTaxGenerateParam;
import com.yonyou.ucf.mdf.rbsm.model.StringListProcessor;
import com.yonyou.ucf.mdf.rbsm.model.VendorBank;
import com.yonyou.ucf.mdf.rbsm.model.VendorInfo;
import com.yonyou.ucf.mdf.rbsm.model.WaData;
import com.yonyou.ucf.mdf.rbsm.model.WaDataQryParam;
import com.yonyou.ucf.mdf.rbsm.model.WaPayRecord;
import com.yonyou.ucf.mdf.rbsm.model.WaPayRecordDetail;
import com.yonyou.ucf.mdf.rbsm.model.WaPayfile;
import com.yonyou.ucf.mdf.rbsm.service.itf.ICommonExpenseQryService;
import com.yonyou.ucf.mdf.rbsm.service.itf.ICommonExpenseService;
import com.yonyou.ucf.mdf.rbsm.service.itf.IExpenseItemService;
import com.yonyou.ucf.mdf.rbsm.service.itf.IFieldRefService;
import com.yonyou.ucf.mdf.rbsm.service.itf.IPayOrgConfigQryService;
import com.yonyou.ucf.mdf.rbsm.service.itf.IVendorQryervice;
import com.yonyou.ucf.mdf.rbsm.service.itf.IWaDataQueryService;
import com.yonyou.ucf.mdf.rbsm.service.itf.IWaPayRecordQryService;
import com.yonyou.ucf.mdf.rbsm.service.itf.IncomeTaxGenerateService;
import com.yonyou.ucf.mdf.rbsm.utils.AppContext;
import com.yonyou.ucf.mdf.rbsm.utils.JSONUtil;
import com.yonyou.workbench.util.Lists;
import com.yonyou.ypd.bill.basic.entity.IBillDO;
import com.yonyou.ypd.bill.infrastructure.service.api.IBillCommonRepository;
import com.yonyou.ypd.bill.infrastructure.service.api.IBillRepository;

import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 *
 *         2025年3月19日
 */
@Slf4j
@Service
public class IncomeTaxGenerateServiceImpl implements IncomeTaxGenerateService {

	@Autowired
	private IFieldRefService fieldRefService;

	@Autowired
	private IWaDataQueryService waDataQueryService;

	@Autowired
	AppContext appContext;

	@Autowired
	IVendorQryervice vendorQryervice;

	@Autowired
	private IExpenseItemService expenseItemService;

	@Autowired
	private ICommonExpenseService commonExpenseService;

	@Autowired
	private IBillCommonRepository billCommonRepository;

	@Autowired
	private IWaPayRecordQryService waPayRecordQryService;

	@Autowired
	private ICommonExpenseQryService commonExpenseQryService;
	@Autowired
	private IBillRepository billRepository;
	@Autowired
	private IPayOrgConfigQryService payOrgConfigQryService;

	@SneakyThrows
	@Override
	public void generateIncomeTax(IncomeTaxGenerateParam param) {

//		List<? extends IBillDO> billList = queryWaPayRecord(param.getPeriod());
		if (!billList.isEmpty()) {
			List<Object> expenseIds = billList.stream().map(v -> v.getAttrValue("expenseId"))
					.collect(Collectors.toList());
			List<Map<String, Object>> bills = queryCommonExpenseByIds(expenseIds);
			if (!bills.isEmpty()) {
				StringBuilder message = new StringBuilder();
				message.append("【");
				message.append(param.getPeriod());
				message.append("期间】");
				message.append("已经生成了税费支付单: \r\n");
				for (Map<String, Object> bill : bills) {
					message.append("【单号：");
					message.append(bill.get("code") + "，");
					message.append("创建人：" + bill.get("creator") + "，");
					message.append("单据日期：" + bill.get("vouchdate") + "】\r\n");
				}
				log.error("查询出报销单：{}", JSONUtil.toJson(bills));
				throw new RuntimeException(message.toString());
			}
		}

		// 根据缴交期间查询发薪方案编码
		List<String> waSchemaCodes = queryWaSchemaCode(param.getPeriod());

		List<WaData> waDatas = Lists.newArrayList();
		List<WaPayfile> waPayfiles = Lists.newArrayList(); // 所有薪资发放单
		List<JSONObject> waDataDetails = Lists.newArrayList(); // 所有发薪明细
		for (String waSchemaCode : waSchemaCodes) {
			WaDataQryParam waDataQryParam = new WaDataQryParam();
			waDataQryParam.setSchemeCode(waSchemaCode);
			waDataQryParam.setPayPeriod(param.getPeriod());

			WaData waData = waDataQueryService.queryWaDataByParam(waDataQryParam);

			waDatas.add(waData);
			waPayfiles.addAll(waData.getWaPayfileVOList());
			for (List<JSONObject> details : waData.getWaPayfileDetailList().values()) {
				waDataDetails.addAll(details);
			}

		}

		List<? extends IBillDO> billList = queryWaPayRecord(param.getPeriod());
		if (!billList.isEmpty()) {
			List<String> expenseIds = billList.stream().map(v -> v.getAttrValue("pkPayfileDetail").toString())
					.collect(Collectors.toList());
			waDataDetails = waDataDetails.stream().filter(w -> !expenseIds.contains(w.getString("id")))
					.collect(Collectors.toList());
			if (waDataDetails.isEmpty()) {
				List<Object> expenseIds = billList.stream().map(v -> v.getAttrValue("expenseId"))
						.collect(Collectors.toList());
				List<Map<String, Object>> bills = queryCommonExpenseByIds(expenseIds);
				if (!bills.isEmpty()) {
					StringBuilder message = new StringBuilder();
					message.append("【");
					message.append(param.getPeriod());
					message.append("期间】");
					message.append("已经生成了税费支付单: \r\n");
					for (Map<String, Object> bill : bills) {
						message.append("【单号：");
						message.append(bill.get("code") + "，");
						message.append("创建人：" + bill.get("creator") + "，");
						message.append("单据日期：" + bill.get("vouchdate") + "】\r\n");
					}
					log.error("查询出报销单：{}", JSONUtil.toJson(bills));
					throw new RuntimeException(message.toString());
				}
			}
		}

		Map<String, List<JSONObject>> waDataDetailGroup = waDataDetails.stream().collect(Collectors.groupingBy(v -> {
			String taxOrgId = v.getString("TAX_ORG_ID");
			String taxDeptId = v.getString("TAX_DEPT_ID");
			return taxOrgId + "@" + taxDeptId;
		})); // 按财务组织和财务部门分组

		// 薪资发放单按id封装成map
		Map<String, WaPayfile> waPayfileMap = waPayfiles.stream().collect(Collectors.toMap(WaPayfile::getId, v -> v));

		Map<String, CommonExpenseBillVO> incomeTaxPayments = generateIncomeTax(waPayfileMap, waDataDetailGroup, param);

		// 生成薪资发放单推送费控记录
		Map<String, WaPayRecord> waPayRecords = generateWaPayRecord(waPayfileMap, waDataDetailGroup);

		CommonExpenseSaveParam saveParam = new CommonExpenseSaveParam();

		for (String key : incomeTaxPayments.keySet()) {
			CommonExpenseBillVO laborPayment = incomeTaxPayments.get(key);
			saveParam.setData(laborPayment);

			CommonExpenseBillVO resultBill = commonExpenseService.saveCommonExpense(saveParam);

			if (StringUtils.isNotBlank(resultBill.getId())) {
				WaPayRecord record = waPayRecords.get(key);
				record.setExpenseId(resultBill.getId());
				record.set_status(ActionEnum.INSERT.getValueInt());
				List<IBillDO> billDOs = Lists.newArrayList(record);
				billCommonRepository.commonSaveBill(billDOs, "WaPayRecord");
			} else {
				log.error("调用通用报销单保存接口返回id为空，参数：{}", JSONUtil.toJson(saveParam));
				throw new RuntimeException("调用通用报销单保存接口返回id为空");
			}

		}

	}

	/**
	 * 根据发薪期间查询发薪方案编码
	 * 
	 * @param period
	 * @return
	 */
	private List<String> queryWaSchemaCode(String period) {
		String sql = creatQueryWaSchemaCodeSql(period);
		List<String> codes = billRepository.queryForList(sql, null, new StringListProcessor());
		if (CollectionUtils.isEmpty(codes)) {
			// TODO 抛出异常或返回信息
			throw new RuntimeException("没有查询到薪资发放单");
		}
		return codes;
	}

	/**
	 * 查询发薪方案编码
	 * 
	 * @param period
	 * @return
	 */
	private String creatQueryWaSchemaCodeSql(String period) {
		StringBuilder sb = new StringBuilder();
		sb.append("select                                            ");
		sb.append("	distinct										 ");
		sb.append("	was.code										 ");
		sb.append("from												 ");
		sb.append("	diwork_wa_mdd.wa_payfile wap					 ");
		sb.append("left join diwork_wa_mdd.wa_scheme_auth wasa on	 ");
		sb.append("	wap.pk_wa_scheme = wasa.id						 ");
		sb.append("left join diwork_wa_mdd.wa_scheme was on			 ");
		sb.append("	wasa.scheme_id = was.id							 ");
		sb.append("left join diwork_wa_mdd.wa_period_detail wapd on	 ");
		sb.append("	wap.pay_period = wapd.id						 ");
		sb.append("where											 ");
		sb.append("	wapd.year = '%s'								 ");
		sb.append("	and wapd.period = '%s'							 ");
		return String.format(sb.toString(), period.substring(0, 4), period.substring(5, 7));
	}

	/**
	 * 根据通用报销单id查询
	 * 
	 * @param expenseIds
	 * @return
	 */
	@SuppressWarnings("unchecked")
	private List<Map<String, Object>> queryCommonExpenseByIds(List<Object> expenseIds) {
		QuerySchema schema = QuerySchema.create();
		schema.addSelect("code,vouchdate,creator");
		schema.addCondition(QueryConditionGroup.and(QueryCondition.name("id").in(expenseIds)));
		return commonExpenseQryService.queryCommonExpense(schema);
	}

	/**
	 * 根据薪资期间查询
	 * 
	 * @param period
	 * @return
	 */
	private List<? extends IBillDO> queryWaPayRecord(String period) {
		QuerySchema schema = QuerySchema.create();
		schema.addSelect("expenseId,WaPayRecordDetailList.pkPayfileDetail as pkPayfileDetail");
		schema.addJoin(new QueryJoin("WaPayRecordDetailList", null, "left"));
		schema.addCondition(
				QueryConditionGroup.and(QueryCondition.name("WaPayRecordDetailList.payPeriodName").eq(period),
						QueryCondition.name("sumType").eq("个税税费")));
		return waPayRecordQryService.queryWaPayRecord(schema);
	}

	/**
	 * 生成薪资发放单推送记录
	 * 
	 * @param waPayfileMap
	 * @param waDataDetailGroup
	 * @return
	 */
	private Map<String, WaPayRecord> generateWaPayRecord(Map<String, WaPayfile> waPayfileMap,
			Map<String, List<JSONObject>> waDataDetailGroup) {

		FieldRef fieldRef = fieldRefService.getFieldRefByKkField("IncomeTax");
		if (fieldRef == null || StringUtils.isEmpty(fieldRef.getRefField())) {
			throw new RuntimeException("没有配置【本次扣税】字段编码【IncomeTax】，请检查【数字化建模>字段映射>客开系统配置>字段映射】是否配置了【IncomeTax】！");
		}
		String incomeTaxField = fieldRef.getRefField();

		Map<String, WaPayRecord> waPayRecords = Maps.newHashMap();
		for (Entry<String, List<JSONObject>> entry : waDataDetailGroup.entrySet()) {
			List<JSONObject> values = entry.getValue();
			List<WaPayRecordDetail> details = values.stream().map(v -> {
				WaPayRecordDetail detail = new WaPayRecordDetail();
				String pkWaPayfile = v.getString("PK_WA_PAYFILE"); // 发放单id
				WaPayfile waPayfile = waPayfileMap.get(pkWaPayfile);
				detail.setCode(waPayfile.getCode());
				detail.setName(waPayfile.getName());
				detail.setScName(waPayfile.getSchemeName());
				detail.setPayPeriodName(waPayfile.getPayPeriodName());
				detail.setPayDate(waPayfile.getPayDate());
				detail.setPkPayfile(pkWaPayfile);
				detail.setPkPayfileDetail(v.getString("id"));
				detail.setStaffId(v.getString("staffId"));
				detail.setPersonalIncomeTax(v.getString(incomeTaxField));
				detail.set_status(ActionEnum.INSERT.getValueInt());
				return detail;
			}).collect(Collectors.toList());

			String[] keys = entry.getKey().split("@"); // 财务组织，财务部门
			WaPayRecord record = new WaPayRecord();
			record.setTaxOrg(keys[0]);
			record.setTaxDept(keys[1]);
			record.setSumType("个税税费");
			record.set_status(ActionEnum.INSERT.getValueInt());
			record.setWaPayRecordDetailList(details);

			waPayRecords.put(entry.getKey(), record);

		}
		return waPayRecords;
	}

	/**
	 * 生成个税支付单
	 * 
	 * @param waPayfileMap
	 * @param waDataDetailGroup
	 * @param param
	 * @return
	 */
	private Map<String, CommonExpenseBillVO> generateIncomeTax(Map<String, WaPayfile> waPayfileMap,
			Map<String, List<JSONObject>> waDataDetailGroup, IncomeTaxGenerateParam param) {

		CurUserInfo userInfo = appContext.getCurUserInfo();

//		String incomeTaxVendorCode = param.getVendorCode();
//		if (StringUtils.isBlank(incomeTaxVendorCode)) {
//			FieldRef fieldRef = fieldRefService.getFieldRefByKkField("incomeTaxVendorCode");
//			if (fieldRef == null || StringUtils.isEmpty(fieldRef.getRefField())) {
//				throw new RuntimeException(
//						"没有配置个税支付单默认供应商编码【incomeTaxVendorCode】，请检查【数字化建模>字段映射>客开系统配置>字段映射】是否配置了【incomeTaxVendorCode】！");
//			}
//			incomeTaxVendorCode = fieldRef.getRefField();
//		}
//		VendorInfo vendorInfo = vendorQryervice.queryVendorByCode2(incomeTaxVendorCode); // 获取供应商信息

		List<Map<String, Object>> payOrgCfgList = payOrgConfigQryService.queryByPayType(PayTypeEnum.INCOME_TAX);
		if (CollectionUtils.isEmpty(payOrgCfgList)) {
			throw new RuntimeException("没有配置个税费用支付相关财务信息，请检查【数字化建模>人力支付财务配置>人力支付财务配置>社保缴交机构财务配置】下配置");
		}
		Map<String, Map<String, Object>> payOrgCfgMap = payOrgCfgList.stream().collect(Collectors.toMap(v -> {
			Object payorg = v.get("cfinaceorg");
			if (payorg != null) {
				return payorg.toString();
			}
			return "";
		}, v -> v, (v1, v2) -> v2));

		List<String> vendorIds = payOrgCfgMap.values().stream().map(m -> {
			Object supplierId = m.get("supplierId");
			if (supplierId != null) {
				return supplierId.toString();
			}
			return null;
		}).filter(Objects::nonNull).distinct().collect(Collectors.toList());

		Map<String, VendorInfo> vendorMap = vendorQryervice.queryByIds(vendorIds);

		FieldRef fieldRef = fieldRefService.getFieldRefByKkField("incomeTaxBustype");
		if (fieldRef == null || StringUtils.isEmpty(fieldRef.getRefField())) {
			throw new RuntimeException(
					"没有配置税费支付单交易类型编码【incomeTaxBustype】，请检查【数字化建模>字段映射>客开系统配置>字段映射】是否配置了【incomeTaxBustype】！");
		}
		String incomeTaxBustype = fieldRef.getRefField();

		// 1、生成个税支付单明细
		Map<String, List<Expensebillb>> expensebillbMap = generateExpensebillb(waDataDetailGroup, waPayfileMap,
				userInfo, payOrgCfgMap, param);
		if (expensebillbMap.isEmpty()) {
			// TODO 没有生成支付单明细数据，可能缴交金额都是0
			throw new RuntimeException("没有查询到可以生成的个税缴交数据或者缴交数据合计金额为0");
		}

		// 2、根据支付单明细，生成费用分摊明细
		Map<String, List<Expapportion>> expapportionMap = generateExpapportion(expensebillbMap);

		// 3、根据支付单明细，生成结算信息
		Map<String, List<Expsettleinfo>> expsettleinfoMap = generateExpsettleinfo(expensebillbMap, vendorMap,
				payOrgCfgMap);

		// 4、创建个税支付单
		Map<String, CommonExpenseBillVO> incomeTaxPayments = Maps.newHashMap();
		for (String key : expensebillbMap.keySet()) {
			String[] keys = key.split("@"); // 财务组织、部门
			List<Expensebillb> expensebillbs = expensebillbMap.get(key);

			Map<String, Object> payOrgCfg = payOrgCfgMap.getOrDefault(keys[0], Collections.emptyMap());

			CommonExpenseBillVO incomeTaxPayment = new CommonExpenseBillVO();
			incomeTaxPayment.setDcostdate(userInfo.getBusDate());
			incomeTaxPayment.setVouchdate(userInfo.getBusDate());
			incomeTaxPayment.setVfinacedeptid(keys[1]);
			incomeTaxPayment.setCfinaceorg(keys[0]);
			incomeTaxPayment.setCaccountorg(keys[0]);
			incomeTaxPayment.setBustype(incomeTaxBustype); // 个税支付单
			incomeTaxPayment.setPk_handlepsn(userInfo.getStaffId());
			incomeTaxPayment.setVhandledeptid(userInfo.getDeptId());
			incomeTaxPayment.setChandleorg(userInfo.getOrgId());
			incomeTaxPayment.setDnatexchratedate(userInfo.getBusDate());
			BigDecimal nexpensemny = BigDecimal.ZERO; // 不含税总额
			BigDecimal nnatexpensemny = BigDecimal.ZERO; // 不含税总额-本币
			BigDecimal nsummny = BigDecimal.ZERO; // 价税总额
			BigDecimal nnatsummny = BigDecimal.ZERO; // 价税总额-本币
			for (Expensebillb expensebillb : expensebillbs) {
				if (StringUtils.isNotBlank(expensebillb.getNexpensemny())) {
					nexpensemny = nexpensemny.add(new BigDecimal(expensebillb.getNexpensemny()));
				}
				if (StringUtils.isNotBlank(expensebillb.getNnatexpensemny())) {
					nnatexpensemny = nnatexpensemny.add(new BigDecimal(expensebillb.getNnatexpensemny()));
				}
				if (StringUtils.isNotBlank(expensebillb.getNsummny())) {
					nsummny = nsummny.add(new BigDecimal(expensebillb.getNsummny()));
				}
				if (StringUtils.isNotBlank(expensebillb.getNnatsummny())) {
					nnatsummny = nnatsummny.add(new BigDecimal(expensebillb.getNnatsummny()));
				}
			}
			incomeTaxPayment.setNexpensemny(nexpensemny.setScale(2, RoundingMode.HALF_UP).toString());
			incomeTaxPayment.setNnatexpensemny(nnatexpensemny.setScale(2, RoundingMode.HALF_UP).toString());
			incomeTaxPayment.setNsummny(nsummny.setScale(2, RoundingMode.HALF_UP).toString());
			incomeTaxPayment.setNnatsummny(nnatsummny.setScale(2, RoundingMode.HALF_UP).toString());
			incomeTaxPayment.setNshouldpaymny(nsummny.setScale(2, RoundingMode.HALF_UP).toString());
			incomeTaxPayment.setNnatshouldpaymny(nnatsummny.setScale(2, RoundingMode.HALF_UP).toString());
			incomeTaxPayment.setPk_cusdoc(expensebillbs.get(0).getPk_cusdoc());
			incomeTaxPayment.setPk_cusdoc_code(expensebillbs.get(0).getPk_cusdoc_code());

			String explain = payOrgCfg.getOrDefault("explain", "").toString();
			if (StringUtils.isNotBlank(explain)) {
				explain = explain.replace("{period}", param.getTaxPeriod().replace("-", "年") + "月");
				incomeTaxPayment.setVreason(explain);
			} else {
				incomeTaxPayment.setVreason(param.getTaxPeriod() + "期间个税支付");
			}

			// 设置用户id，接口查询会报错
//            socialSecurityPayment.setCreatorId(userInfo.getUserId());
//            socialSecurityPayment.setCreatorId("7cdf19ac-4902-497e-8690-45f3e9181972");
			incomeTaxPayment.setCreator_code(userInfo.getUserCode());

			// 子表数据
			incomeTaxPayment.setExpensebillbs(expensebillbs);
			incomeTaxPayment.setExpapportions(expapportionMap.get(key));
			incomeTaxPayment.setExpsettleinfos(expsettleinfoMap.get(key));
			if (CollectionUtils.isNotEmpty(expsettleinfoMap.get(key))) { // 社保结算单付款附言
				Expsettleinfo expsettleinfo = expsettleinfoMap.get(key).get(0);
				JSONObject expsettleinfoDcs = new JSONObject();
				expsettleinfoDcs.put("BX76", incomeTaxPayment.getVreason());
				expsettleinfo.setExpsettleinfoDcs(expsettleinfoDcs);
			}

//			JSONObject expensebillDcs = new JSONObject();
//
//			incomeTaxPayment.setExpensebillDcs(expensebillDcs);

			incomeTaxPayment.setExtend2(param.getPeriod()); // 扩展字段-期间
			incomeTaxPayment.setExtend3(param.getTaxPeriod()); // 扩展字段-纳税期间

			incomeTaxPayments.put(key, incomeTaxPayment);
		}
		return incomeTaxPayments;
	}

	/**
	 * 生成结算明细
	 * 
	 * @param expensebillbMap
	 * @param vendorInfo
	 * @return
	 */
	private Map<String, List<Expsettleinfo>> generateExpsettleinfo(Map<String, List<Expensebillb>> expensebillbMap,
			Map<String, VendorInfo> vendorMap, Map<String, Map<String, Object>> payOrgCfgMap) {
		Map<String, List<Expsettleinfo>> expsettleinfoMap = Maps.newHashMap();
		for (Entry<String, List<Expensebillb>> entry : expensebillbMap.entrySet()) {
			String[] keys = entry.getKey().split("@");// 财务组织、财务部门
			List<Expensebillb> values = entry.getValue();
			BigDecimal nsummny = BigDecimal.ZERO; // 结算合计付款金额
			for (Expensebillb expensebillb : values) {
				if (StringUtils.isNotBlank(expensebillb.getNsummny())) {
					nsummny = nsummny.add(new BigDecimal(expensebillb.getNsummny()));
				}
			}
			String nsummnyStr = nsummny.toString();
			Expsettleinfo expsettleinfo = new Expsettleinfo();

			Expensebillb expensebillb = values.get(0);

			Map<String, Object> payOrgCfg = payOrgCfgMap.getOrDefault(keys[0], Collections.emptyMap());

			VendorInfo vendorInfo = vendorMap.get(expensebillb.getPk_cusdoc());

			VendorBank vendorBank = getDefaultBank(vendorInfo);
			if (vendorBank != null) {
				expsettleinfo.setVbankaccount(vendorBank.getAccount()); // TODO 收款方账号
				expsettleinfo.setVbankaccname(
						vendorBank.getAccountname() != null ? vendorBank.getAccountname().getZh_CN() : ""); // TODO
				// 收款方户名
				expsettleinfo.setPk_bankdoc(vendorBank.getOpenaccountbank()); // TODO 收款方开户行
				expsettleinfo.setPk_banktype(vendorBank.getBank()); // TODO 收款银行类别
				expsettleinfo.setPk_cusdocbank(vendorBank.getId()); // TODO 供应商银行账户id
				expsettleinfo.setPk_cusdocbank_code(null);// TODO 供应商银行账户编码(付款类型igathertype为1时，id和编码必填一项)
				expsettleinfo.setAccttype(vendorBank.getAccountType());
			}
			expsettleinfo.setPk_cusdoc(vendorInfo.getId()); // TODO 供应商id(付款类型igathertype为1时，id和编码必填一项)
			expsettleinfo.setPk_cusdoc_code(vendorInfo.getCode()); // TODO 供应商编码(付款类型igathertype为1时，id和编码必填一项)
			expsettleinfo.setIgathertype("1"); // TODO 收款类型(0:个人;1:供应商;2:客户)
			expsettleinfo.setPk_balatype(payOrgCfg.getOrDefault("pkBalatype", "").toString()); // 结算方式id(结算方式编码和id必填一项)
			if (payOrgCfg.get("pkBalatype") == null) {
				expsettleinfo.setPk_balatype_code("system_0001"); // 如果没有配置默认结算方，则默认银行转账(结算方式编码和id必填一项)
			}
			expsettleinfo.setBalatypesrvattr("0"); // TODO 结算方式业务属性(0:银行业务;1:现金业务）
			expsettleinfo.setCenterpriseorg(expensebillb.getCfinaceorg());
			expsettleinfo.setPk_enterprisebankacct(payOrgCfg.getOrDefault("enterprisebank", "").toString());// 企业银行账户(支持id和code，结算方式为银行转账时必填)
			expsettleinfo.setVbankaccount_opp(payOrgCfg.getOrDefault("account", "").toString()); // 付款银行账号(结算方式为银行转账时必填)
			expsettleinfo.setAccttype_opp(payOrgCfg.getOrDefault("acctType", "").toString());// 付款账户类型（0:基本;1:一般;2:临时;3:专用）
			expsettleinfo.setVbankaccname_opp(payOrgCfg.getOrDefault("acctName", "").toString()); // 付款账户户名
			expsettleinfo.setPk_banktype_opp(payOrgCfg.getOrDefault("bank", "").toString()); // 付款银行类别(支持id和code，结算方式为银行转账时必填)
			expsettleinfo.setPk_bankdoc_opp(payOrgCfg.getOrDefault("bankNumber", "").toString()); // 付款开户行(支持id和code，结算方式为银行转账时必填)
			expsettleinfo.setPk_enterprisecashacct(null); // TODO 企业现金账户(支持id和code，结算方式为现金时必填)
			expsettleinfo.setDnatexchratedate(expensebillb.getDnatexchratedate());
			expsettleinfo.setNsummny(nsummnyStr);
			expsettleinfo.setNsettlesummny(nsummnyStr);
			expsettleinfo.setNnatsettlesummny(nsummnyStr);

			expsettleinfoMap.put(entry.getKey(), Arrays.asList(expsettleinfo));

		}
		return expsettleinfoMap;
	}

	/**
	 * 获取供应商默认银行账户，如果不存在默认银行账户，取第一条
	 *
	 * @param vendorInfo
	 * @return
	 */
	private VendorBank getDefaultBank(VendorInfo vendorInfo) {
		if (vendorInfo == null || CollectionUtils.isEmpty(vendorInfo.getVendorbanks())) {
			return null;
		}
		return vendorInfo.getVendorbanks().stream().filter(VendorBank::isDefaultbank).findFirst()
				.orElse(vendorInfo.getVendorbanks().get(0));
	}

	/**
	 * 生成分摊数据明细
	 * 
	 * @param expensebillbMap
	 * @return
	 */
	private Map<String, List<Expapportion>> generateExpapportion(Map<String, List<Expensebillb>> expensebillbMap) {
		Map<String, List<Expapportion>> expapportionMap = Maps.newHashMap();
		for (Entry<String, List<Expensebillb>> entry : expensebillbMap.entrySet()) {
			List<Expensebillb> values = entry.getValue();
			List<Expapportion> expapportionList = values.stream().map(v -> {
				Expapportion expapportion = new Expapportion();
				expapportion.setVfinacedeptid(v.getVfinacedeptid());
				expapportion.setCfinaceorg(v.getCfinaceorg());
				expapportion.setCaccountorg(v.getCaccountorg());
				expapportion.setPk_busimemo(v.getPk_busimemo());
				expapportion.setDnatexchratedate(v.getDnatexchratedate());
				expapportion.setNapportmny(v.getNsummny());
				expapportion.setNnatapportmny(v.getNnatsummny());
				expapportion.setNapportnotaxmny(v.getNexpensemny());
				expapportion.setNnatapportnotaxmny(v.getNnatexpensemny());
				return expapportion;
			}).collect(Collectors.toList());
			expapportionMap.put(entry.getKey(), expapportionList);
		}
		return expapportionMap;
	}

	/**
	 * 生成个税支付单明细
	 * 
	 * @param waDataDetailGroup
	 * @param waPayfileMap
	 * @param userInfo
	 * @param vendorInfo
	 * @return
	 */
	private Map<String, List<Expensebillb>> generateExpensebillb(Map<String, List<JSONObject>> waDataDetailGroup,
			Map<String, WaPayfile> waPayfileMap, CurUserInfo userInfo, Map<String, Map<String, Object>> payOrgCfgMap,
			IncomeTaxGenerateParam param) {

		// 查询费用项目
		Map<String, ExpenseItem> expenseMap = expenseItemService.getAllExpenseItem();

		FieldRef fieldRef = fieldRefService.getFieldRefByKkField("IncomeTax");
		if (fieldRef == null || StringUtils.isEmpty(fieldRef.getRefField())) {
			throw new RuntimeException("没有配置【本次扣税】字段编码【IncomeTax】，请检查【数字化建模>字段映射>客开系统配置>字段映射】是否配置了【IncomeTax】！");
		}
		String incomeTaxField = fieldRef.getRefField().toUpperCase();

		fieldRef = fieldRefService.getFieldRefByKkField("个人所得税");
		if (fieldRef == null || StringUtils.isEmpty(fieldRef.getRefField())) {
			throw new RuntimeException("没有配置【个人所得税】费用项目编码，请检查【数字化建模>字段映射>客开系统配置>字段映射】是否配置了【个人所得税】！");
		}
		String incomeTaxFeeCode = fieldRef.getRefField().toUpperCase();

		Map<String, List<Expensebillb>> expensebillbMap = Maps.newHashMap();

		for (Entry<String, List<JSONObject>> entry : waDataDetailGroup.entrySet()) {

			String[] keys = entry.getKey().split("@");// 财务组织、财务部门
			List<JSONObject> values = entry.getValue();

			Map<String, Object> payOrgCfg = payOrgCfgMap.getOrDefault(keys[0], Collections.emptyMap());

			List<Expensebillb> expensebillbs = Lists.newArrayList();

			BigDecimal incomeTaxAmount = BigDecimal.ZERO; // 个税合计

			for (JSONObject value : values) {
				BigDecimal taxAmount = value.getBigDecimal(incomeTaxField);
				if (taxAmount != null) {
					incomeTaxAmount = incomeTaxAmount.add(taxAmount);
				}
			}

			if (incomeTaxAmount.compareTo(BigDecimal.ZERO) != 0) {
				Expensebillb expensebillbP = new Expensebillb(); // 创建报销单明细
				String personalAmountStr = incomeTaxAmount.setScale(2, RoundingMode.HALF_UP).toString();
				ExpenseItem expenseP = expenseMap.get(incomeTaxFeeCode);// 费用项目个人所得税
				if (expenseP == null) {
					// 费用项目为空
					throw new RuntimeException("没有查询到个人所得税费用项目");
				}
				// 费用项目信息
				expensebillbP.setPk_busimemo(expenseP.getId());
				expensebillbP.setDnatexchratedate(userInfo.getBusDate());
				expensebillbP.setPk_handlepsn(userInfo.getStaffId());
				expensebillbP.setVhandledeptid(userInfo.getDeptId());
				expensebillbP.setChandleorg(userInfo.getOrgId());
				expensebillbP.setCaccountorg(keys[0]);
				expensebillbP.setCfinaceorg(keys[0]);
				expensebillbP.setVfinacedeptid(keys[1]);
				expensebillbP.setPk_cusdoc(payOrgCfg.getOrDefault("supplierId", "").toString()); // TODO 供应商id
				expensebillbP.setPk_cusdoc_code(payOrgCfg.getOrDefault("supplierCode", "").toString()); // TODO 供应商编码
				expensebillbP.setNexpensemny(personalAmountStr);
				expensebillbP.setNnatexpensemny(personalAmountStr);
				expensebillbP.setNsummny(personalAmountStr);
				expensebillbP.setNnatsummny(personalAmountStr);
				expensebillbP.setNshouldpaymny(personalAmountStr);
				expensebillbP.setNnatshouldpaymny(personalAmountStr);
				expensebillbP.setNpaymentmny(personalAmountStr);
				expensebillbP.setNnatpaymentmny(personalAmountStr);

				// 使用特征和实施新增的特征项冲突，所以使用扩展字段来做
//				JSONObject expensebillBDcs = new JSONObject(); // 特征组
//				expensebillBDcs.put("tz_period", valueEntry.getKey()); // 期间
//				expensebillbP.setExpensebillBDcs(expensebillBDcs);

				expensebillbP.setExtend4(param.getPeriod()); // TODO 期间
				expensebillbP.setExtend5(param.getTaxPeriod()); // TODO 纳税期间

				expensebillbs.add(expensebillbP);
			}

			if (!expensebillbs.isEmpty()) {
				expensebillbMap.put(entry.getKey(), expensebillbs);
			}
		}
		return expensebillbMap;
	}

}
