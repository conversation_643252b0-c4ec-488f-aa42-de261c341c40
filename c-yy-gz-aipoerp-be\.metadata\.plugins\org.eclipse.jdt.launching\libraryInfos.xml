<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<libraryInfos>
    <libraryInfo home="C:\Users\<USER>\.p2\pool\plugins\org.eclipse.justj.openjdk.hotspot.jre.full.win32.x86_64_21.0.7.v20250502-0916\jre" version="21.0.7"/>
    <libraryInfo home="D:\Program Files\Java\jdk1.8.0_291" version="1.8.0_291">
        <bootpath>
            <entry path="D:\Program Files\Java\jdk1.8.0_291\jre\lib\resources.jar"/>
            <entry path="D:\Program Files\Java\jdk1.8.0_291\jre\lib\rt.jar"/>
            <entry path="D:\Program Files\Java\jdk1.8.0_291\jre\lib\sunrsasign.jar"/>
            <entry path="D:\Program Files\Java\jdk1.8.0_291\jre\lib\jsse.jar"/>
            <entry path="D:\Program Files\Java\jdk1.8.0_291\jre\lib\jce.jar"/>
            <entry path="D:\Program Files\Java\jdk1.8.0_291\jre\lib\charsets.jar"/>
            <entry path="D:\Program Files\Java\jdk1.8.0_291\jre\lib\jfr.jar"/>
            <entry path="D:\Program Files\Java\jdk1.8.0_291\jre\classes"/>
        </bootpath>
        <extensionDirs>
            <entry path="D:\Program Files\Java\jdk1.8.0_291\jre\lib\ext"/>
            <entry path="C:\Windows\Sun\Java\lib\ext"/>
        </extensionDirs>
        <endorsedDirs>
            <entry path="D:\Program Files\Java\jdk1.8.0_291\jre\lib\endorsed"/>
        </endorsedDirs>
    </libraryInfo>
    <libraryInfo home="C:\Users\<USER>\.p2\pool\plugins\org.eclipse.justj.openjdk.hotspot.jre.full.win32.x86_64_21.0.7.v20250502-0916" version="21.0.7">
        <bootpath>
            <entry path="null"/>
        </bootpath>
        <extensionDirs>
            <entry path="null"/>
        </extensionDirs>
        <endorsedDirs>
            <entry path="null"/>
        </endorsedDirs>
    </libraryInfo>
</libraryInfos>
