<?xml version="1.0" encoding="UTF-8"?>
<session version="1.0">
<refactoring comment="Extract method &apos;private List&lt;Map&lt;String,Object&gt;&gt; queryBusimemos(Set&lt;String&gt; pk_busimemos)&apos; from &apos;com.yonyou.ucf.mdf.iris.plugin.PayIndexRequestPluginImpl.findBusinessBill(...)&apos; to &apos;com.yonyou.ucf.mdf.iris.plugin.PayIndexRequestPluginImpl&apos;&#x0D;&#x0A;- Original project: &apos;dev-c-yy-gz-aipoerp-service&apos;&#x0D;&#x0A;- Method name: &apos;queryBusimemos&apos;&#x0D;&#x0A;- Destination type: &apos;com.yonyou.ucf.mdf.iris.plugin.PayIndexRequestPluginImpl&apos;&#x0D;&#x0A;- Declared visibility: &apos;private&apos;" comments="false" description="Extract method &apos;queryBusimemos&apos;" destination="0" exceptions="false" flags="786434" id="org.eclipse.jdt.ui.extract.method" input="/src\/main\/java=/optional=/true=/=/maven.pomderived=/true=/&lt;com.yonyou.ucf.mdf.iris.plugin{PayIndexRequestPluginImpl.java" name="queryBusimemos" replace="false" selection="7575 927" stamp="1750993348133" version="1.0" visibility="2"/>
</session>