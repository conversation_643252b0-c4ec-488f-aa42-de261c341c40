<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<launchConfiguration type="org.eclipse.jdt.launching.localJavaApplication">
    <listAttribute key="org.eclipse.debug.core.MAPPED_RESOURCE_PATHS">
        <listEntry value="/dev-c-yy-gz-aipoerp-bootstrap/src/main/java/com/yonyou/ucf/mdf/MDFApplication.java"/>
    </listAttribute>
    <listAttribute key="org.eclipse.debug.core.MAPPED_RESOURCE_TYPES">
        <listEntry value="1"/>
    </listAttribute>
    <booleanAttribute key="org.eclipse.jdt.launching.ATTR_EXCLUDE_TEST_CODE" value="true"/>
    <stringAttribute key="org.eclipse.jdt.launching.CLASSPATH_PROVIDER" value="org.eclipse.m2e.launchconfig.classpathProvider"/>
    <stringAttribute key="org.eclipse.jdt.launching.MAIN_TYPE" value="com.yonyou.ucf.mdf.MDFApplication"/>
    <stringAttribute key="org.eclipse.jdt.launching.MODULE_NAME" value=""/>
    <stringAttribute key="org.eclipse.jdt.launching.PROJECT_ATTR" value="dev-c-yy-gz-aipoerp-bootstrap"/>
    <stringAttribute key="org.eclipse.jdt.launching.SOURCE_PATH_PROVIDER" value="org.eclipse.m2e.launchconfig.sourcepathProvider"/>
</launchConfiguration>
