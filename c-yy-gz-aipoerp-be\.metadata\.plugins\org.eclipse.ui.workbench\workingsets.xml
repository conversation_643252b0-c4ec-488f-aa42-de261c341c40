<?xml version="1.0" encoding="UTF-8"?>
<workingSetManager>
<workingSet editPageId="org.eclipse.jdt.internal.ui.DynamicSourcesWorkingSet" factoryID="org.eclipse.ui.internal.WorkingSetFactory" id="1750035537739_0" label="Java Main Sources" name="Java Main Sources"/>
<workingSet editPageId="org.eclipse.jdt.internal.ui.DynamicSourcesWorkingSet" factoryID="org.eclipse.ui.internal.WorkingSetFactory" id="1750035537751_1" label="Java Test Sources" name="Java Test Sources"/>
<workingSet editPageId="org.eclipse.ui.resourceWorkingSetPage" factoryID="org.eclipse.ui.internal.WorkingSetFactory" id="1750035562688_3" label="c-yy-gz-aipoerp" name="c-yy-gz-aipoerp">
<item factoryID="org.eclipse.ui.internal.model.ResourceFactory" path="/dev-c-yy-gz-aipoerp-api" type="4"/>
<item factoryID="org.eclipse.ui.internal.model.ResourceFactory" path="/dev-c-yy-gz-aipoerp-app" type="4"/>
<item factoryID="org.eclipse.ui.internal.model.ResourceFactory" path="/dev-c-yy-gz-aipoerp-bootstrap" type="4"/>
<item factoryID="org.eclipse.ui.internal.model.ResourceFactory" path="/dev-c-yy-gz-aipoerp-infrastructure" type="4"/>
<item factoryID="org.eclipse.ui.internal.model.ResourceFactory" path="/dev-c-yy-gz-aipoerp-service" type="4"/>
</workingSet>
<workingSet aggregate="true" factoryID="org.eclipse.ui.internal.WorkingSetFactory" id="1750035538361_2" label="Window Working Set" name="Aggregate for window 1750035538361"/>
</workingSetManager>